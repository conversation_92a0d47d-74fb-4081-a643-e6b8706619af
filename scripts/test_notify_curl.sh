#!/bin/bash

# 使用curl直接测试微信支付回调接口
echo "=== 微信支付回调接口测试 (使用curl) ==="

# 检查服务是否运行
echo "1. 检查服务状态..."
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "错误: 服务未运行，请先启动服务"
    echo "运行命令: go run cmd/server/main.go"
    exit 1
fi

echo "服务运行正常"

# 构造微信支付V3回调数据
# 注意：这是一个简化的测试数据，实际的微信回调数据需要正确的加密和签名

# 当前时间戳
TIMESTAMP=$(date +%s)
ORDER_NO="TEST_NOTIFY_${TIMESTAMP}"

echo -e "\n2. 测试支付成功回调..."

# 构造回调数据（简化版，不包含加密）
NOTIFY_DATA='{
  "id": "notify_'${TIMESTAMP}'",
  "create_time": "'$(date -u +%Y-%m-%dT%H:%M:%S+08:00)'",
  "event_type": "TRANSACTION.SUCCESS",
  "resource_type": "encrypt-resource",
  "resource": {
    "algorithm": "AEAD_AES_256_GCM",
    "ciphertext": "test_encrypted_data",
    "associated_data": "transaction",
    "nonce": "test_nonce_123"
  },
  "summary": "支付成功"
}'

echo "发送回调数据:"
echo "$NOTIFY_DATA" | jq .

# 发送回调请求
echo -e "\n发送回调请求..."
RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "Content-Type: application/json" \
  -H "Wechatpay-Timestamp: ${TIMESTAMP}" \
  -H "Wechatpay-Nonce: test_nonce_${TIMESTAMP}" \
  -H "Wechatpay-Serial: 7042C24F0C04F7DEC91393E505163FCA3170B46B" \
  -H "Wechatpay-Signature: test_signature" \
  -d "$NOTIFY_DATA" \
  http://localhost:8080/notify/wechat)

# 解析响应
HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d')

echo "响应状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 回调测试成功"
else
    echo "❌ 回调测试失败"
fi

echo -e "\n3. 测试退款成功回调..."

# 构造退款回调数据
REFUND_NOTIFY_DATA='{
  "id": "refund_notify_'${TIMESTAMP}'",
  "create_time": "'$(date -u +%Y-%m-%dT%H:%M:%S+08:00)'",
  "event_type": "REFUND.SUCCESS",
  "resource_type": "encrypt-resource",
  "resource": {
    "algorithm": "AEAD_AES_256_GCM",
    "ciphertext": "test_refund_encrypted_data",
    "associated_data": "transaction",
    "nonce": "test_refund_nonce_123"
  },
  "summary": "退款成功"
}'

echo "发送退款回调数据:"
echo "$REFUND_NOTIFY_DATA" | jq .

# 发送退款回调请求
echo -e "\n发送退款回调请求..."
REFUND_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "Content-Type: application/json" \
  -H "Wechatpay-Timestamp: ${TIMESTAMP}" \
  -H "Wechatpay-Nonce: test_refund_nonce_${TIMESTAMP}" \
  -H "Wechatpay-Serial: 7042C24F0C04F7DEC91393E505163FCA3170B46B" \
  -H "Wechatpay-Signature: test_refund_signature" \
  -d "$REFUND_NOTIFY_DATA" \
  http://localhost:8080/notify/wechat)

# 解析退款响应
REFUND_HTTP_CODE=$(echo "$REFUND_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
REFUND_RESPONSE_BODY=$(echo "$REFUND_RESPONSE" | sed '/HTTP_CODE:/d')

echo "退款响应状态码: $REFUND_HTTP_CODE"
echo "退款响应内容: $REFUND_RESPONSE_BODY"

if [ "$REFUND_HTTP_CODE" = "200" ]; then
    echo "✅ 退款回调测试成功"
else
    echo "❌ 退款回调测试失败"
fi

echo -e "\n=== 测试完成 ==="
echo "注意: 这是简化的测试，实际的微信回调需要正确的加密数据和签名验证"
