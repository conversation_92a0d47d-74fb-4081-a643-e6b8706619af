#!/bin/bash

# 退款功能测试脚本
echo "=== PayCore 退款功能测试 ==="

# 检查服务是否运行
echo "1. 检查服务状态..."
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "❌ 错误: 服务未运行，请先启动服务"
    echo "运行命令: go run cmd/server/main.go"
    exit 1
fi

echo "✅ 服务运行正常"

# 运行退款示例
echo -e "\n2. 运行退款示例..."
cd "$(dirname "$0")/.."

if [ -f "examples/refund_demo.go" ]; then
    echo "执行退款示例 (订单号: 20250823233320e3fddb)..."
    go run examples/refund_demo.go
else
    echo "❌ 错误: 退款示例文件不存在"
    exit 1
fi

echo -e "\n=== 测试完成 ==="
