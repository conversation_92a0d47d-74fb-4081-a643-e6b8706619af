#!/bin/bash

# 微信支付回调测试脚本
echo "=== 微信支付回调测试 ==="

# 检查服务是否运行
echo "1. 检查服务状态..."
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "错误: 服务未运行，请先启动服务"
    echo "运行命令: go run cmd/server/main.go"
    exit 1
fi

echo "服务运行正常"

# 运行回调测试
echo -e "\n2. 运行微信支付回调测试..."
cd "$(dirname "$0")/.."

if [ -f "test/simple_wechat_notify_test.go" ]; then
    echo "运行简化版回调测试..."
    go run test/simple_wechat_notify_test.go
else
    echo "错误: 测试文件不存在"
    exit 1
fi

echo -e "\n=== 测试完成 ==="
