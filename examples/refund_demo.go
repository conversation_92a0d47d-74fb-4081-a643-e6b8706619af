package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// PayCoreClient 支付核心客户端
type PayCoreClient struct {
	ClientID   string
	AppSecret  string
	BaseURL    string
	HTTPClient *http.Client
}

// NewPayCoreClient 创建新的客户端
func NewPayCoreClient(clientID, appSecret, baseURL string) *PayCoreClient {
	return &PayCoreClient{
		ClientID:  clientID,
		AppSecret: appSecret,
		BaseURL:   baseURL,
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// GetOrder 获取订单信息
func (c *PayCoreClient) GetOrder(orderNo string) (*Response, error) {
	return c.request("GET", fmt.Sprintf("/api/v1/orders/%s", orderNo), nil)
}

// RefundOrder 申请退款
func (c *PayCoreClient) RefundOrder(orderNo, appRefundNo string, refundAmount float64, refundReason string) (*Response, error) {
	req := map[string]interface{}{
		"app_refund_no": appRefundNo,
		"refund_amount": refundAmount,
		"refund_reason": refundReason,
	}
	return c.request("POST", fmt.Sprintf("/api/v1/orders/%s/refund", orderNo), req)
}

// request 发送HTTP请求
func (c *PayCoreClient) request(method, path string, body interface{}) (*Response, error) {
	url := c.BaseURL + path

	// 准备请求体
	var reqBody []byte
	var err error
	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("marshal request body: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	// 设置Content-Type
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// 生成认证头
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := c.generateNonce()
	signature := c.generateSignature(method, path, reqBody, timestamp, nonce)

	// 设置认证头
	req.Header.Set("X-API-Key", c.ClientID)
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response: %w", err)
	}

	// 解析响应
	var response Response
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("unmarshal response: %w", err)
	}

	return &response, nil
}

// generateNonce 生成随机字符串
func (c *PayCoreClient) generateNonce() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 16)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateSignature 生成签名
func (c *PayCoreClient) generateSignature(method, path string, body []byte, timestamp, nonce string) string {
	params := make(map[string]string)

	// 解析JSON参数
	if len(body) > 0 {
		var jsonParams map[string]interface{}
		if err := json.Unmarshal(body, &jsonParams); err == nil {
			for key, value := range jsonParams {
				params[key] = fmt.Sprintf("%v", value)
			}
		}
	}

	// 添加认证参数
	params["api_key"] = c.ClientID
	params["timestamp"] = timestamp
	params["nonce"] = nonce

	// 生成签名
	return c.calculateSignature(params)
}

// calculateSignature 计算签名
func (c *PayCoreClient) calculateSignature(params map[string]string) string {
	// 1. 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		if key != "signature" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	signStr := strings.Join(parts, "&") + "&key=" + c.AppSecret

	// 3. 计算HMAC-SHA256
	h := hmac.New(sha256.New, []byte(c.AppSecret))
	h.Write([]byte(signStr))

	return hex.EncodeToString(h.Sum(nil))
}

// 退款示例
func main() {
	fmt.Println("=== PayCore 退款功能示例 ===")

	// 创建客户端
	client := NewPayCoreClient(
		"robot_user123",                    // 客户端ID
		"7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F", // 应用密钥
		"http://localhost:8080",            // 服务器地址
	)

	// 指定要退款的订单号
	orderNo := "20250824103721fc61cc"

	fmt.Printf("1. 查询订单信息: %s\n", orderNo)

	// 先查询订单状态
	orderResp, err := client.GetOrder(orderNo)
	if err != nil {
		fmt.Printf("❌ 查询订单失败: %v\n", err)
		return
	}

	fmt.Printf("订单查询响应: %+v\n", orderResp)

	if orderResp.Code != 200 {
		fmt.Printf("❌ 订单查询失败: %s\n", orderResp.Message)
		return
	}

	// 解析订单信息
	orderData, ok := orderResp.Data.(map[string]interface{})
	if !ok {
		fmt.Println("❌ 订单数据格式错误")
		return
	}

	status, _ := orderData["status"].(string)
	totalAmount, _ := orderData["total_amount"].(float64)
	subject, _ := orderData["subject"].(string)

	fmt.Printf("✅ 订单信息:\n")
	fmt.Printf("   - 订单号: %s\n", orderNo)
	fmt.Printf("   - 商品名称: %s\n", subject)
	fmt.Printf("   - 订单状态: %s\n", status)
	fmt.Printf("   - 订单金额: %.2f 元\n", totalAmount)

	// 检查订单状态是否可以退款
	if status != "PAID" {
		fmt.Printf("❌ 订单状态为 %s，无法申请退款（只有已支付的订单才能退款）\n", status)
		return
	}

	fmt.Println("\n2. 申请退款")

	// 生成业务方退款单号
	appRefundNo := fmt.Sprintf("REFUND_%d", time.Now().Unix())

	// 退款金额（使用0.01元进行测试）
	refundAmount := 0.01
	refundReason := "用户申请退款 - 测试退款功能"

	fmt.Printf("退款参数:\n")
	fmt.Printf("   - 业务退款单号: %s\n", appRefundNo)
	fmt.Printf("   - 退款金额: %.2f 元\n", refundAmount)
	fmt.Printf("   - 退款原因: %s\n", refundReason)

	// 申请退款
	refundResp, err := client.RefundOrder(orderNo, appRefundNo, refundAmount, refundReason)
	if err != nil {
		fmt.Printf("❌ 申请退款失败: %v\n", err)
		return
	}

	fmt.Printf("退款申请响应: %+v\n", refundResp)

	if refundResp.Code != 200 {
		fmt.Printf("❌ 退款申请失败: %s\n", refundResp.Message)
		return
	}

	// 解析退款响应
	refundData, ok := refundResp.Data.(map[string]interface{})
	if !ok {
		fmt.Println("❌ 退款响应数据格式错误")
		return
	}

	refundNo, _ := refundData["refund_no"].(string)
	refundStatus, _ := refundData["status"].(string)

	fmt.Printf("✅ 退款申请成功:\n")
	fmt.Printf("   - 退款单号: %s\n", refundNo)
	fmt.Printf("   - 退款状态: %s\n", refundStatus)
	fmt.Printf("   - 退款金额: %.2f 元\n", refundAmount)

	fmt.Println("\n=== 退款示例完成 ===")
	fmt.Println("注意事项:")
	fmt.Println("1. 只有状态为 'PAID' 的订单才能申请退款")
	fmt.Println("2. 退款金额不能超过订单原始金额")
	fmt.Println("3. app_refund_no 必须是唯一的业务方退款单号")
	fmt.Println("4. 退款处理可能需要一定时间，请耐心等待")
}
