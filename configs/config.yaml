server:
  port: 8080
  mode: debug
  base_url: "https://6b51df8a6941.ngrok-free.app"  # 生产环境需要修改为实际的外网地址，如: https://your-domain.com

database:
  host: **************
  port: 3306
  username: root
  password: "aCbNqFi0KrCZ7T4q-9Df"
  database: pay_core
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  addr: **************:6389
  password: "foobared123"
  db: 0

# 客户端认证配置
auth:
  app_secret: "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F"
  timestamp_tolerance: 300  # 5分钟（秒）
  nonce_ttl: 600s          # 10分钟（Duration格式）

payment:
  wechat:
    app_id: "wxcf9a42f63c23a637"                    # 微信应用ID
    mch_id: "1264055901"                    # 商户号
    is_prod: false                # 是否生产环境

    # V3 API必需配置
    apiv3_key: "ZxQr5VpJ9sW4nGfYhLdK8cT3bRm7eNg2"                 # V3 API密钥（32位）
    serial_no: "7042C24F0C04F7DEC91393E505163FCA3170B46B"                 # 商户证书序列号（40位十六进制）

    # 商户私钥配置（二选一）
    private_key: ""               # 商户私钥内容（PEM格式）
    private_key_path: "configs/1264055901_20250822_cert/apiclient_key.pem"          # 商户私钥文件路径

    # 微信支付平台证书配置（用于验签，可选但推荐）
    platform_cert_path: "configs/pub_key.pem"       # 微信支付平台证书文件路径
    platform_cert: ""            # 微信支付平台证书内容（PEM格式）
    platform_cert_serial_no: "PUB_KEY_ID_0112640559012025082200382286000401"  # 微信支付平台证书序列号

log:
  level: info
  format: json
  output: stdout



scheduler:
  close_expired_orders:
    enabled: true
    cron: "0 0 */1 * * *"  # 改为每小时执行一次（兜底机制）
    timeout_minutes: 5
    batch_size: 100
    scan_days: 7
    lock_key: "pay_core:close_expired_orders"
    lock_ttl: 300
  daily_reconcile:
    enabled: true
    cron: "0 0 2 * * *"
    timeout_minutes: 30
    channels: ["wechat"]
    delay_days: 1
    lock_key: "pay_core:daily_reconcile"
    lock_ttl: 1800

# Redis延迟队列配置
queue:
  enabled: true
  expire_check_interval: 30  # 订单过期检查间隔（秒）
  poll_check_interval: 10    # 订单轮询检查间隔（秒）
  max_poll_count: 5          # 最大轮询次数
  first_poll_delay: 30       # 首次轮询延迟（秒）

# 机器人账户服务配置
robot:
  base_url: "http://chat.backend.efrobot.com"
  shared_key: "default-shared-key-change-in-production"
  timeout: 30
