# 生产环境配置示例
server:
  port: 8080
  mode: release
  base_url: "https://your-domain.com"  # 替换为您的实际域名

database:
  host: "your-db-host"
  port: 3306
  user: "your-db-user"
  password: "your-db-password"
  dbname: "pay_core"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  addr: "your-redis-host:6379"
  password: "your-redis-password"
  db: 0
  pool_size: 10
  min_idle_conns: 5

payment:
  wechat:
    app_id: "your-wechat-app-id"
    mch_id: "your-wechat-mch-id"
    api_key: "your-wechat-api-key"
    cert_path: "/path/to/wechat/cert.pem"
    key_path: "/path/to/wechat/key.pem"
  alipay:
    app_id: "your-alipay-app-id"
    private_key_path: "/path/to/alipay/private_key.pem"
    public_key_path: "/path/to/alipay/public_key.pem"
    is_sandbox: false

robot:
  base_url: "http://chat.backend.efrobot.com"
  shared_key: "your-robot-shared-key"
  timeout: 30

scheduler:
  enabled: true
  close_expired_orders_cron: "0 */5 * * * *"  # 每5分钟执行一次
  reconciliation_cron: "0 0 2 * * *"          # 每天凌晨2点执行

log:
  level: "info"
  format: "json"
  output: "file"
  file_path: "/var/log/pay-core/app.log"
  max_size: 100
  max_backups: 7
  max_age: 30
  compress: true
