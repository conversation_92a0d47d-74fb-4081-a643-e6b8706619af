package lock

import (
	"context"
	"fmt"
	"time"

	"pay-core/pkg/logger"

	"github.com/redis/go-redis/v9"
)

// RedisLock Redis分布式锁
type RedisLock struct {
	redis  *redis.Client
	key    string
	value  string
	expiry time.Duration
}

// NewRedisLock 创建Redis分布式锁
func NewRedisLock(redis *redis.Client, key string, expiry time.Duration) *RedisLock {
	return &RedisLock{
		redis:  redis,
		key:    key,
		value:  fmt.Sprintf("%d", time.Now().UnixNano()), // 使用时间戳作为唯一值
		expiry: expiry,
	}
}

// TryLock 尝试获取锁
func (l *RedisLock) TryLock(ctx context.Context) (bool, error) {
	result, err := l.redis.SetNX(ctx, l.key, l.value, l.expiry).Result()
	if err != nil {
		logger.WithError(err).WithField("lock_key", l.key).Error("Failed to acquire lock")
		return false, err
	}

	if result {
		logger.WithFields(map[string]interface{}{
			"lock_key":   l.key,
			"lock_value": l.value,
			"expiry":     l.expiry,
		}).Debug("Lock acquired successfully")
	} else {
		logger.WithField("lock_key", l.key).Debug("Lock already held by another process")
	}

	return result, nil
}

// Unlock 释放锁
func (l *RedisLock) Unlock(ctx context.Context) error {
	// 使用Lua脚本确保只有持有锁的进程才能释放锁
	luaScript := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`

	result, err := l.redis.Eval(ctx, luaScript, []string{l.key}, l.value).Result()
	if err != nil {
		logger.WithError(err).WithField("lock_key", l.key).Error("Failed to release lock")
		return err
	}

	if result.(int64) == 1 {
		logger.WithField("lock_key", l.key).Debug("Lock released successfully")
	} else {
		logger.WithField("lock_key", l.key).Warn("Lock was not held by this process")
	}

	return nil
}

// WithLock 使用锁执行函数
func WithLock(ctx context.Context, redis *redis.Client, lockKey string, expiry time.Duration, fn func() error) error {
	lock := NewRedisLock(redis, lockKey, expiry)

	// 尝试获取锁
	acquired, err := lock.TryLock(ctx)
	if err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	if !acquired {
		logger.WithField("lock_key", lockKey).Info("Lock not acquired, skipping execution")
		return nil // 锁未获取到，直接返回（不是错误）
	}

	// 确保释放锁
	defer func() {
		if unlockErr := lock.Unlock(ctx); unlockErr != nil {
			logger.WithError(unlockErr).WithField("lock_key", lockKey).Error("Failed to unlock")
		}
	}()

	// 执行业务逻辑
	return fn()
}

// PaymentLockKey 生成支付处理锁的键
func PaymentLockKey(orderNo string) string {
	return fmt.Sprintf("payment_lock:%s", orderNo)
}

// RefundLockKey 生成退款处理锁的键
func RefundLockKey(refundNo string) string {
	return fmt.Sprintf("refund_lock:%s", refundNo)
}
