package service

import (
	"context"
	"fmt"
	"time"

	"pay-core/pkg/logger"
	"pay-core/pkg/queue"

	"github.com/redis/go-redis/v9"
)

// QueueService 队列处理服务接口
type QueueService interface {
	Start() error
	Stop() error
	AddOrderExpireTask(orderNo string, expireTime time.Time) error
	AddOrderPollTask(orderNo string, pollTime time.Time, pollCount int) error
	RemoveOrderExpireTask(orderNo string) error
	RemoveOrderPollTask(orderNo string, pollCount int) error
	AddRefundExpireTask(refundNo string, expireTime time.Time) error
	AddRefundPollTask(refundNo string, pollTime time.Time, pollCount int) error
	RemoveRefundExpireTask(refundNo string) error
	RemoveRefundPollTask(refundNo string, pollCount int) error
}

// queueService 队列处理服务实现
type queueService struct {
	delayQueue     *queue.DelayQueue
	paymentService PaymentService
	redis          *redis.Client
	stopChan       chan struct{}
	running        bool
}

// NewQueueService 创建队列处理服务
func NewQueueService(redis *redis.Client, paymentService PaymentService) QueueService {
	delayQueue := queue.NewDelayQueue(redis, "pay_core")

	return &queueService{
		delayQueue:     delayQueue,
		paymentService: paymentService,
		redis:          redis,
		stopChan:       make(chan struct{}),
		running:        false,
	}
}

// Start 启动队列处理服务
func (qs *queueService) Start() error {
	if qs.running {
		return fmt.Errorf("queue service is already running")
	}

	qs.running = true
	logger.Info("Starting queue service")

	// 启动订单过期任务处理器
	go qs.processOrderExpireTasks()

	// 启动订单轮询任务处理器
	go qs.processOrderPollTasks()

	// 启动退款过期任务处理器
	go qs.processRefundExpireTasks()

	// 启动退款轮询任务处理器
	go qs.processRefundPollTasks()

	logger.Info("Queue service started successfully")
	return nil
}

// Stop 停止队列处理服务
func (qs *queueService) Stop() error {
	if !qs.running {
		return nil
	}

	logger.Info("Stopping queue service")
	qs.running = false
	close(qs.stopChan)

	logger.Info("Queue service stopped")
	return nil
}

// AddOrderExpireTask 添加订单过期任务
func (qs *queueService) AddOrderExpireTask(orderNo string, expireTime time.Time) error {
	task := queue.CreateOrderExpireTask(orderNo, expireTime)

	err := qs.delayQueue.AddTask(context.Background(), task)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"order_no":    orderNo,
			"expire_time": expireTime,
		}).Error("Failed to add order expire task")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"order_no":    orderNo,
		"expire_time": expireTime,
		"task_id":     task.ID,
	}).Debug("Added order expire task to queue")

	return nil
}

// AddOrderPollTask 添加订单轮询任务
func (qs *queueService) AddOrderPollTask(orderNo string, pollTime time.Time, pollCount int) error {
	task := queue.CreateOrderPollTask(orderNo, pollTime, pollCount)

	err := qs.delayQueue.AddTask(context.Background(), task)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"order_no":   orderNo,
			"poll_time":  pollTime,
			"poll_count": pollCount,
		}).Error("Failed to add order poll task")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"order_no":   orderNo,
		"poll_time":  pollTime,
		"poll_count": pollCount,
		"task_id":    task.ID,
	}).Debug("Added order poll task to queue")

	return nil
}

// RemoveOrderExpireTask 移除订单过期任务
func (qs *queueService) RemoveOrderExpireTask(orderNo string) error {
	taskID := fmt.Sprintf("expire_%s", orderNo)
	return qs.delayQueue.RemoveTask(context.Background(), queue.TaskTypeOrderExpire, taskID)
}

// RemoveOrderPollTask 移除订单轮询任务
func (qs *queueService) RemoveOrderPollTask(orderNo string, pollCount int) error {
	taskID := fmt.Sprintf("poll_%s_%d", orderNo, pollCount)
	return qs.delayQueue.RemoveTask(context.Background(), queue.TaskTypeOrderPoll, taskID)
}

// processOrderExpireTasks 处理订单过期任务
func (qs *queueService) processOrderExpireTasks() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-qs.stopChan:
			logger.Info("Order expire task processor stopped")
			return
		case <-ticker.C:
			qs.handleOrderExpireTasks()
		}
	}
}

// processOrderPollTasks 处理订单轮询任务
func (qs *queueService) processOrderPollTasks() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-qs.stopChan:
			logger.Info("Order poll task processor stopped")
			return
		case <-ticker.C:
			qs.handleOrderPollTasks()
		}
	}
}

// handleOrderExpireTasks 处理订单过期任务
func (qs *queueService) handleOrderExpireTasks() {
	ctx := context.Background()

	tasks, err := qs.delayQueue.GetReadyTasks(ctx, queue.TaskTypeOrderExpire, 50)
	if err != nil {
		logger.WithError(err).Error("Failed to get ready order expire tasks")
		return
	}

	if len(tasks) == 0 {
		return
	}

	logger.WithField("task_count", len(tasks)).Info("Processing order expire tasks")

	for _, task := range tasks {
		orderNo, ok := task.Data["order_no"].(string)
		if !ok {
			logger.WithField("task_id", task.ID).Error("Invalid order_no in expire task")
			continue
		}

		if err := qs.processOrderExpireTask(ctx, orderNo); err != nil {
			logger.WithError(err).WithFields(map[string]interface{}{
				"task_id":  task.ID,
				"order_no": orderNo,
			}).Error("Failed to process order expire task")

			// 重试任务
			if retryErr := qs.delayQueue.RetryTask(ctx, task, 5*time.Minute); retryErr != nil {
				logger.WithError(retryErr).Error("Failed to retry order expire task")
			}
		} else {
			logger.WithFields(map[string]interface{}{
				"task_id":  task.ID,
				"order_no": orderNo,
			}).Info("Successfully processed order expire task")
		}
	}
}

// handleOrderPollTasks 处理订单轮询任务
func (qs *queueService) handleOrderPollTasks() {
	ctx := context.Background()

	tasks, err := qs.delayQueue.GetReadyTasks(ctx, queue.TaskTypeOrderPoll, 30)
	if err != nil {
		logger.WithError(err).Error("Failed to get ready order poll tasks")
		return
	}

	if len(tasks) == 0 {
		return
	}

	logger.WithField("task_count", len(tasks)).Info("Processing order poll tasks")

	for _, task := range tasks {
		orderNo, ok := task.Data["order_no"].(string)
		if !ok {
			logger.WithField("task_id", task.ID).Error("Invalid order_no in poll task")
			continue
		}

		pollCount, ok := task.Data["poll_count"].(float64) // JSON数字默认为float64
		if !ok {
			logger.WithField("task_id", task.ID).Error("Invalid poll_count in poll task")
			continue
		}

		if err := qs.processOrderPollTask(ctx, orderNo, int(pollCount)); err != nil {
			logger.WithError(err).WithFields(map[string]interface{}{
				"task_id":    task.ID,
				"order_no":   orderNo,
				"poll_count": int(pollCount),
			}).Error("Failed to process order poll task")

			// 重试任务
			if retryErr := qs.delayQueue.RetryTask(ctx, task, 2*time.Minute); retryErr != nil {
				logger.WithError(retryErr).Error("Failed to retry order poll task")
			}
		} else {
			logger.WithFields(map[string]interface{}{
				"task_id":    task.ID,
				"order_no":   orderNo,
				"poll_count": int(pollCount),
			}).Info("Successfully processed order poll task")
		}
	}
}

// processOrderExpireTask 处理单个订单过期任务
func (qs *queueService) processOrderExpireTask(ctx context.Context, orderNo string) error {
	// 调用PaymentService的关闭过期订单方法
	return qs.paymentService.CloseExpiredOrder(ctx, orderNo)
}

// processOrderPollTask 处理单个订单轮询任务
func (qs *queueService) processOrderPollTask(ctx context.Context, orderNo string, pollCount int) error {
	// 调用PaymentService的轮询订单状态方法
	return qs.paymentService.PollOrderStatus(ctx, orderNo, pollCount)
}

// AddRefundExpireTask 添加退款过期任务
func (qs *queueService) AddRefundExpireTask(refundNo string, expireTime time.Time) error {
	task := queue.CreateRefundExpireTask(refundNo, expireTime)

	err := qs.delayQueue.AddTask(context.Background(), task)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"refund_no":   refundNo,
			"expire_time": expireTime,
		}).Error("Failed to add refund expire task")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"refund_no":   refundNo,
		"expire_time": expireTime,
		"task_id":     task.ID,
	}).Debug("Added refund expire task to queue")

	return nil
}

// AddRefundPollTask 添加退款轮询任务
func (qs *queueService) AddRefundPollTask(refundNo string, pollTime time.Time, pollCount int) error {
	task := queue.CreateRefundPollTask(refundNo, pollTime, pollCount)

	err := qs.delayQueue.AddTask(context.Background(), task)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"refund_no":  refundNo,
			"poll_time":  pollTime,
			"poll_count": pollCount,
		}).Error("Failed to add refund poll task")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"refund_no":  refundNo,
		"poll_time":  pollTime,
		"poll_count": pollCount,
		"task_id":    task.ID,
	}).Debug("Added refund poll task to queue")

	return nil
}

// RemoveRefundExpireTask 移除退款过期任务
func (qs *queueService) RemoveRefundExpireTask(refundNo string) error {
	taskID := fmt.Sprintf("refund_expire_%s", refundNo)
	return qs.delayQueue.RemoveTask(context.Background(), "refund_expire", taskID)
}

// RemoveRefundPollTask 移除退款轮询任务
func (qs *queueService) RemoveRefundPollTask(refundNo string, pollCount int) error {
	taskID := fmt.Sprintf("refund_poll_%s_%d", refundNo, pollCount)
	return qs.delayQueue.RemoveTask(context.Background(), "refund_poll", taskID)
}

// processRefundExpireTasks 处理退款过期任务
func (qs *queueService) processRefundExpireTasks() {
	ctx := context.Background()
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	logger.Info("Started refund expire task processor")

	for {
		select {
		case <-qs.stopChan:
			logger.Info("Stopping refund expire task processor")
			return
		case <-ticker.C:
			tasks, err := qs.delayQueue.GetReadyTasks(ctx, queue.TaskTypeRefundExpire, 10)
			if err != nil {
				logger.WithError(err).Error("Failed to get ready refund expire tasks")
				continue
			}

			for _, task := range tasks {
				refundNo, ok := task.Data["refund_no"].(string)
				if !ok {
					logger.WithField("task_id", task.ID).Error("Invalid refund_no in refund expire task")
					continue
				}

				if err := qs.processRefundExpireTask(ctx, refundNo); err != nil {
					logger.WithError(err).WithField("refund_no", refundNo).Error("Failed to process refund expire task")
				}
			}
		}
	}
}

// processRefundPollTasks 处理退款轮询任务
func (qs *queueService) processRefundPollTasks() {
	ctx := context.Background()
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	logger.Info("Started refund poll task processor")

	for {
		select {
		case <-qs.stopChan:
			logger.Info("Stopping refund poll task processor")
			return
		case <-ticker.C:
			tasks, err := qs.delayQueue.GetReadyTasks(ctx, queue.TaskTypeRefundPoll, 10)
			if err != nil {
				logger.WithError(err).Error("Failed to get ready refund poll tasks")
				continue
			}

			for _, task := range tasks {
				refundNo, ok := task.Data["refund_no"].(string)
				if !ok {
					logger.WithField("task_id", task.ID).Error("Invalid refund_no in refund poll task")
					continue
				}

				pollCount, ok := task.Data["poll_count"].(float64)
				if !ok {
					logger.WithField("task_id", task.ID).Error("Invalid poll_count in refund poll task")
					continue
				}

				if err := qs.processRefundPollTask(ctx, refundNo, int(pollCount)); err != nil {
					logger.WithError(err).WithFields(map[string]interface{}{
						"refund_no":  refundNo,
						"poll_count": int(pollCount),
					}).Error("Failed to process refund poll task")
				}
			}
		}
	}
}

// processRefundExpireTask 处理单个退款过期任务
func (qs *queueService) processRefundExpireTask(ctx context.Context, refundNo string) error {
	// 调用PaymentService的关闭过期退款方法
	return qs.paymentService.CloseExpiredRefund(ctx, refundNo)
}

// processRefundPollTask 处理单个退款轮询任务
func (qs *queueService) processRefundPollTask(ctx context.Context, refundNo string, pollCount int) error {
	// 调用PaymentService的轮询退款状态方法
	return qs.paymentService.PollRefundStatus(ctx, refundNo, pollCount)
}
