package service

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/internal/repository"
	"pay-core/pkg/lock"
	"pay-core/pkg/logger"
	"pay-core/pkg/payment"
	"pay-core/pkg/robot"
	"pay-core/pkg/types"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// PaymentService 支付服务接口
type PaymentService interface {
	CreateOrder(ctx context.Context, appID uint64, req *model.CreateOrderRequest) (*model.CreateOrderResponse, error)
	GetOrder(ctx context.Context, appID uint64, orderNo string) (*model.PaymentOrder, error)
	QueryOrder(ctx context.Context, appID uint64, req *model.QueryOrderRequest) (*model.PaymentOrder, error)
	HandleNotify(ctx context.Context, channel string, req *http.Request) error
	RefundOrder(ctx context.Context, appID uint64, orderNo string, req *model.RefundOrderRequest) (*model.RefundOrder, error)
	CloseExpiredOrders(ctx context.Context) error
	CloseExpiredOrder(ctx context.Context, orderNo string) error
	PollOrderStatus(ctx context.Context, orderNo string, pollCount int) error
	CloseExpiredRefund(ctx context.Context, refundNo string) error
	PollRefundStatus(ctx context.Context, refundNo string, pollCount int) error
}

// paymentService 支付服务实现
type paymentService struct {
	paymentOrderRepo  repository.PaymentOrderRepository
	refundOrderRepo   repository.RefundOrderRepository
	paymentRecordRepo repository.PaymentRecordRepository
	refundRecordRepo  repository.RefundRecordRepository
	notifyLogRepo     repository.NotifyLogRepository
	appRepo           repository.AppRepository
	paymentClient     *payment.Client
	robotClient       *robot.Client
	queueService      QueueService
	redis             *redis.Client
	config            *config.Config
}

// NewPaymentService 创建支付服务
func NewPaymentService(
	paymentOrderRepo repository.PaymentOrderRepository,
	refundOrderRepo repository.RefundOrderRepository,
	paymentRecordRepo repository.PaymentRecordRepository,
	refundRecordRepo repository.RefundRecordRepository,
	notifyLogRepo repository.NotifyLogRepository,
	appRepo repository.AppRepository,
	paymentClient *payment.Client,
	robotClient *robot.Client,
	redis *redis.Client,
	config *config.Config,
) PaymentService {
	service := &paymentService{
		paymentOrderRepo:  paymentOrderRepo,
		refundOrderRepo:   refundOrderRepo,
		paymentRecordRepo: paymentRecordRepo,
		refundRecordRepo:  refundRecordRepo,
		notifyLogRepo:     notifyLogRepo,
		appRepo:           appRepo,
		paymentClient:     paymentClient,
		robotClient:       robotClient,
		redis:             redis,
		config:            config,
	}

	// 创建队列服务（避免循环依赖）
	service.queueService = NewQueueService(redis, service)

	return service
}

// CreateOrder 创建支付订单
func (s *paymentService) CreateOrder(ctx context.Context, appID uint64, req *model.CreateOrderRequest) (*model.CreateOrderResponse, error) {
	// 检查应用是否存在
	app, err := s.appRepo.GetByID(ctx, appID)
	if err != nil {
		return nil, errors.New("app not found")
	}
	if !app.IsActive() {
		return nil, errors.New("app is not active")
	}

	// 生成系统订单号
	orderNo := generateOrderNo()

	// 处理商户订单号：如果为空，使用系统订单号
	merchantOrderNo := req.MerchantOrderNo
	if merchantOrderNo == "" {
		merchantOrderNo = orderNo
	} else {
		// 检查业务订单号是否已存在
		if _, err := s.paymentOrderRepo.GetByAppOrderNo(ctx, appID, merchantOrderNo); err == nil {
			return nil, errors.New("merchant order number already exists")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}

	// 处理商品标题：如果为空，使用默认模板
	subject := req.Subject
	if subject == "" {
		subject = fmt.Sprintf("超级语音交互充值%.2f元", req.Amount)
	}

	// 设置默认过期时间
	if req.ExpireMinutes == 0 {
		req.ExpireMinutes = 30 // 默认30分钟过期
	}
	expireTime := types.Now().Add(time.Duration(req.ExpireMinutes) * time.Minute)

	// 设置默认支付方式和渠道
	if req.PaymentMethod == "" {
		req.PaymentMethod = "native"
	}
	if req.PaymentChannel == "" {
		req.PaymentChannel = "wechat"
	}

	// 确定AppUserID
	appUserID := req.AppUserID
	if appUserID == "" && req.UserID != nil {
		appUserID = fmt.Sprintf("%d", *req.UserID) // 兼容旧字段
	}

	// 创建订单
	order := &model.PaymentOrder{
		OrderNo:     orderNo,
		AppID:       appID,
		AppOrderNo:  merchantOrderNo,
		AppUserID:   appUserID,
		PayChannel:  req.PaymentChannel,
		TotalAmount: req.Amount,
		Subject:     subject,
		Body:        req.Body,
		Status:      model.OrderStatusWaitingPay,
		ExpireTime:  expireTime,
		Attach:      req.Attach,
	}

	// 保存订单到数据库
	if err := s.paymentOrderRepo.Create(ctx, order); err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// 调用支付接口创建支付订单
	payInfo, err := s.createPaymentOrder(ctx, order, req)
	if err != nil {
		// 如果支付接口调用失败，删除已创建的订单
		if deleteErr := s.paymentOrderRepo.Delete(ctx, order.ID); deleteErr != nil {
			logger.WithError(deleteErr).Error("Failed to delete order after payment creation failed")
		}
		return nil, fmt.Errorf("failed to create payment order: %w", err)
	}

	// 更新订单支付信息
	order.PayInfo = payInfo
	if err := s.paymentOrderRepo.Update(ctx, order); err != nil {
		logger.WithError(err).Error("Failed to update order with pay info")
		return nil, fmt.Errorf("failed to update order with pay info: %w", err)
	}

	// 添加到Redis延迟队列
	if s.queueService != nil {
		// 添加订单过期任务
		if err := s.queueService.AddOrderExpireTask(orderNo, expireTime.Time); err != nil {
			logger.WithError(err).WithField("order_no", orderNo).Error("Failed to add order expire task")
		}

		// 添加第一次轮询任务（30秒后）
		firstPollTime := time.Now().Add(30 * time.Second)
		if err := s.queueService.AddOrderPollTask(orderNo, firstPollTime, 1); err != nil {
			logger.WithError(err).WithField("order_no", orderNo).Error("Failed to add order poll task")
		}

		logger.WithFields(map[string]interface{}{
			"order_no":        orderNo,
			"expire_time":     expireTime,
			"first_poll_time": firstPollTime,
		}).Debug("Added order to delay queues")
	}

	// 构建响应
	response := &model.CreateOrderResponse{
		OrderNo:     orderNo,
		TotalAmount: req.Amount,
		PayInfo:     payInfo,
		ExpireTime:  expireTime,
		PayChannel:  req.PaymentChannel,
	}

	return response, nil
}

// GetOrder 获取订单信息
func (s *paymentService) GetOrder(ctx context.Context, appID uint64, orderNo string) (*model.PaymentOrder, error) {
	order, err := s.paymentOrderRepo.GetByOrderNo(ctx, orderNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("order not found")
		}
		return nil, err
	}

	// 检查应用权限
	if order.AppID != appID {
		return nil, errors.New("access denied")
	}

	// 如果订单状态为待支付，查询支付平台状态
	if order.Status == model.OrderStatusWaitingPay && !order.IsExpired() {
		s.syncOrderStatus(ctx, order)
	}

	return order, nil
}

// QueryOrder 查询订单状态
func (s *paymentService) QueryOrder(ctx context.Context, appID uint64, req *model.QueryOrderRequest) (*model.PaymentOrder, error) {
	var order *model.PaymentOrder
	var err error

	// 根据不同参数查询订单
	if req.OrderNo != "" {
		order, err = s.paymentOrderRepo.GetByOrderNo(ctx, req.OrderNo)
	} else if req.AppOrderNo != "" {
		order, err = s.paymentOrderRepo.GetByAppOrderNo(ctx, appID, req.AppOrderNo)
	} else if req.OutTradeNo != "" {
		order, err = s.paymentOrderRepo.GetByOutTradeNo(ctx, req.OutTradeNo)
	} else {
		return nil, errors.New("missing query parameter")
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("order not found")
		}
		return nil, err
	}

	// 检查应用权限
	if order.AppID != appID {
		return nil, errors.New("access denied")
	}

	// 如果订单状态为待支付，查询支付平台状态
	if order.Status == model.OrderStatusWaitingPay && !order.IsExpired() {
		s.syncOrderStatus(ctx, order)
	}

	return order, nil
}

// HandleNotify 处理支付回调通知
func (s *paymentService) HandleNotify(ctx context.Context, channel string, req *http.Request) error {
	logger.WithFields(map[string]interface{}{
		"channel": channel,
		"method":  req.Method,
		"url":     req.URL.String(),
	}).Info("Received payment notification")

	// 读取请求体用于日志记录
	body, err := io.ReadAll(req.Body)
	if err != nil {
		logger.WithError(err).Error("Failed to read request body for logging")
		return fmt.Errorf("failed to read request body: %w", err)
	}

	// 重新设置请求体，以便后续解析使用
	req.Body = io.NopCloser(bytes.NewReader(body))

	// 解析回调数据
	notifyData, err := s.paymentClient.ParseNotify(ctx, channel, req)
	if err != nil {
		// 记录解析失败的回调日志
		s.recordNotifyLogWithType(ctx, "", channel, string(body), "FAILED", err.Error(), "PLATFORM", "UNKNOWN")
		logger.WithError(err).WithField("channel", channel).Error("Failed to parse payment notification")
		return fmt.Errorf("failed to parse notification: %w", err)
	}

	logger.WithFields(map[string]interface{}{
		"channel":        channel,
		"order_no":       notifyData.OrderNo,
		"transaction_id": notifyData.TransactionID,
		"amount":         notifyData.Amount,
		"status":         notifyData.Status,
		"type":           notifyData.Type,
	}).Info("Parsed payment notification")

	// 处理回调并记录结果
	var processErr error
	var notifyType string

	switch notifyData.Type {
	case payment.NotifyTypePaid:
		notifyType = "PAYMENT"
		processErr = s.handlePaymentSuccess(ctx, notifyData)
	case payment.NotifyTypeRefunded:
		notifyType = "REFUND"
		processErr = s.handleRefundSuccess(ctx, notifyData)
	default:
		notifyType = "UNKNOWN"
		processErr = fmt.Errorf("unknown notification type: %s", notifyData.Type)
		logger.WithField("type", notifyData.Type).Warn("Unknown notification type")
	}

	// 记录回调日志
	status := "SUCCESS"
	errorMsg := ""
	if processErr != nil {
		status = "FAILED"
		errorMsg = processErr.Error()
	}

	s.recordNotifyLogWithType(ctx, notifyData.OrderNo, channel, string(body), status, errorMsg, "PLATFORM", notifyType)

	return processErr
}

// generateRefundNo 生成退款单号
func generateRefundNo() string {
	return "R" + generateOrderNo()
}

// RefundOrder 申请退款
func (s *paymentService) RefundOrder(ctx context.Context, appID uint64, orderNo string, req *model.RefundOrderRequest) (*model.RefundOrder, error) {
	// 获取订单
	order, err := s.paymentOrderRepo.GetByOrderNo(ctx, orderNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("order not found")
		}
		return nil, err
	}

	// 检查应用权限
	if order.AppID != appID {
		return nil, errors.New("access denied")
	}

	// 检查订单状态
	if !order.CanRefund() {
		return nil, errors.New("order cannot be refunded")
	}

	// 检查退款金额
	if req.RefundAmount.GreaterThan(order.TotalAmount) {
		return nil, errors.New("refund amount exceeds order amount")
	}

	// 创建退款订单
	refundOrder := &model.RefundOrder{
		RefundNo:     generateRefundNo(),
		OrderNo:      orderNo,
		AppID:        appID,
		AppRefundNo:  req.AppRefundNo,
		RefundAmount: req.RefundAmount,
		RefundReason: req.RefundReason,
		Status:       model.RefundStatusProcessing,
	}

	if err := s.refundOrderRepo.Create(ctx, refundOrder); err != nil {
		return nil, fmt.Errorf("failed to create refund order: %w", err)
	}

	// 调用支付平台退款接口
	refundReq := &payment.RefundRequest{
		OrderNo:       orderNo,
		RefundNo:      refundOrder.RefundNo,
		TransactionID: order.OutTradeNo,
		RefundAmount:  req.RefundAmount,
		TotalAmount:   order.TotalAmount,
		RefundReason:  req.RefundReason,
		NotifyURL:     s.buildRefundNotifyURL(order.PayChannel),
		Channel:       order.PayChannel,
	}

	refundResp, err := s.paymentClient.Refund(refundReq)
	if err != nil {
		// 退款申请失败，更新退款订单状态
		refundOrder.Status = model.RefundStatusFailed
		if updateErr := s.refundOrderRepo.Update(ctx, refundOrder); updateErr != nil {
			logger.WithError(updateErr).Error("Failed to update refund order status after refund failed")
		}
		return nil, fmt.Errorf("failed to create refund request: %w", err)
	}

	// 更新退款订单信息
	refundOrder.OutRefundNo = refundResp.OutRefundNo
	if err := s.refundOrderRepo.Update(ctx, refundOrder); err != nil {
		logger.WithError(err).Error("Failed to update refund order with out_refund_no")
		// 不返回错误，因为退款申请已经成功
	}

	logger.WithFields(map[string]interface{}{
		"refund_no":     refundOrder.RefundNo,
		"order_no":      orderNo,
		"out_refund_no": refundResp.OutRefundNo,
		"refund_amount": req.RefundAmount,
		"refund_status": refundResp.Status,
	}).Info("Refund request created successfully")

	return refundOrder, nil
}

// recordNotifyLogWithType 记录回调日志（指定类型）
func (s *paymentService) recordNotifyLogWithType(ctx context.Context, orderNo, channel, requestBody, status, errorMsg, notifySource, notifyType string) {
	// 获取应用ID（如果订单号不为空）
	var appID *uint64
	if orderNo != "" {
		if order, err := s.paymentOrderRepo.GetByOrderNo(ctx, orderNo); err == nil {
			appID = &order.AppID
		}
	}

	notifyLog := &model.NotifyLog{
		OrderNo:      orderNo,
		AppID:        appID,
		NotifyType:   notifyType,
		NotifySource: notifySource,
		RequestBody:  requestBody,
		Status:       status,
		ErrorMsg:     errorMsg,
		CreatedAt:    types.Now(),
	}

	if err := s.notifyLogRepo.Create(ctx, notifyLog); err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"order_no":      orderNo,
			"notify_type":   notifyType,
			"notify_source": notifySource,
			"status":        status,
		}).Error("Failed to create notify log")
	}
}

// recordNotifyLog 记录回调日志（自动判断类型）
func (s *paymentService) recordNotifyLog(ctx context.Context, orderNo, channel, requestBody, status, errorMsg, notifySource string) {
	// 确定通知类型
	var notifyType string
	if channel == "wechat" || channel == "alipay" {
		// 根据URL路径判断是支付回调还是退款回调
		// 这里简化处理，实际应该根据请求路径或内容判断
		notifyType = "PAYMENT" // 默认为支付回调
	}

	s.recordNotifyLogWithType(ctx, orderNo, channel, requestBody, status, errorMsg, notifySource, notifyType)
}

// notifyRobotService 通知机器人账户服务
func (s *paymentService) notifyRobotService(ctx context.Context, order *model.PaymentOrder, amount decimal.Decimal) error {
	// 检查是否配置了机器人服务
	if s.robotClient == nil {
		logger.Info("Robot client not configured, skipping robot service notification")
		return nil
	}

	// 获取deviceSN（appUserID）
	deviceSN := order.AppUserID
	if deviceSN == "" {
		logger.WithField("order_no", order.OrderNo).Warn("AppUserID is empty, cannot notify robot service")
		return fmt.Errorf("appUserID is empty")
	}

	// 构建充值请求
	rechargeReq := &robot.RechargeRequest{
		Amount:      amount,
		OrderID:     order.OrderNo,
		Description: "账户充值",
	}

	logger.WithFields(map[string]interface{}{
		"order_no":    order.OrderNo,
		"device_sn":   deviceSN,
		"amount":      amount,
		"description": rechargeReq.Description,
	}).Info("Calling robot service for recharge")

	// 获取请求信息用于日志记录
	_, requestBody, err := s.robotClient.GetRequestInfo(deviceSN, rechargeReq)
	if err != nil {
		s.recordNotifyLogWithType(ctx, order.OrderNo, "robot", "", "FAILED", err.Error(), "APP", "RECHARGE")
		return fmt.Errorf("failed to build request info: %w", err)
	}

	// 调用机器人服务
	resp, err := s.robotClient.Recharge(ctx, deviceSN, rechargeReq)

	// 记录业务回调日志
	status := "SUCCESS"
	errorMsg := ""
	responseBody := ""

	if err != nil {
		status = "FAILED"
		errorMsg = err.Error()
	} else if resp != nil {
		respBytes, _ := json.Marshal(resp)
		responseBody = string(respBytes)
		if resp.Code != 0 {
			status = "FAILED"
			errorMsg = resp.Message
		}
	}

	// 记录到notify_logs
	notifyLog := &model.NotifyLog{
		OrderNo:      order.OrderNo,
		AppID:        &order.AppID,
		NotifyType:   "RECHARGE",
		NotifySource: "APP",
		NotifyURL:    s.robotClient.GetBaseURL() + "/api/v1/robot/recharge",
		RequestBody:  requestBody,
		ResponseCode: 200, // HTTP状态码
		ResponseBody: responseBody,
		Status:       status,
		ErrorMsg:     errorMsg,
		CreatedAt:    types.Now(),
	}

	if logErr := s.notifyLogRepo.Create(ctx, notifyLog); logErr != nil {
		logger.WithError(logErr).WithField("order_no", order.OrderNo).Error("Failed to create robot service notify log")
	}

	if err != nil {
		return fmt.Errorf("robot service call failed: %w", err)
	}

	logger.WithFields(map[string]interface{}{
		"order_no":      order.OrderNo,
		"device_sn":     deviceSN,
		"amount":        amount,
		"robot_code":    resp.Code,
		"robot_message": resp.Message,
	}).Info("Robot service recharge completed successfully")

	return nil
}

// buildNotifyURL 构建支付回调URL
func (s *paymentService) buildNotifyURL(channel string) string {
	baseURL := s.config.Server.BaseURL
	return fmt.Sprintf("%s/notify/%s", baseURL, channel)
}

// buildRefundNotifyURL 构建退款回调URL
func (s *paymentService) buildRefundNotifyURL(channel string) string {
	baseURL := s.config.Server.BaseURL
	return fmt.Sprintf("%s/notify/%s/refund", baseURL, channel)
}

// CloseExpiredOrders 关闭过期订单
func (s *paymentService) CloseExpiredOrders(ctx context.Context) error {
	batchSize := 100
	scanDays := 7

	orders, err := s.paymentOrderRepo.GetExpiredOrders(ctx, batchSize, scanDays)
	if err != nil {
		return err
	}

	if len(orders) == 0 {
		logger.Info("No expired orders found")
		return nil
	}

	logger.WithField("count", len(orders)).Info("Found expired orders to close")

	successCount := 0
	for _, order := range orders {
		// 先尝试关闭支付平台订单
		if err := s.closePaymentPlatformOrder(ctx, order); err != nil {
			logger.WithError(err).WithField("order_no", order.OrderNo).
				Warn("Failed to close payment platform order, continuing with local close")
		}

		// 关闭本地订单
		order.Status = model.OrderStatusClosed
		if err := s.paymentOrderRepo.Update(ctx, order); err != nil {
			logger.WithError(err).WithField("order_no", order.OrderNo).Error("Failed to close expired order")
		} else {
			logger.WithField("order_no", order.OrderNo).Info("Closed expired order")
			successCount++
		}
	}

	logger.WithFields(map[string]interface{}{
		"total":   len(orders),
		"success": successCount,
		"failed":  len(orders) - successCount,
	}).Info("Completed closing expired orders")
	return nil
}

// closePaymentPlatformOrder 关闭支付平台订单
func (s *paymentService) closePaymentPlatformOrder(ctx context.Context, order *model.PaymentOrder) error {
	// 只关闭等待支付状态的订单
	if order.Status != model.OrderStatusWaitingPay {
		logger.WithFields(map[string]interface{}{
			"order_no": order.OrderNo,
			"status":   order.Status,
		}).Debug("Skip closing payment platform order for non-waiting-pay status")
		return nil
	}

	closeReq := &payment.CloseOrderRequest{
		OrderNo: order.OrderNo,
		Channel: order.PayChannel,
	}

	// 使用重试机制关闭支付平台订单
	err := s.retryWithBackoff(ctx, func() error {
		resp, closeErr := s.paymentClient.CloseOrder(closeReq)
		if closeErr != nil {
			return closeErr
		}

		if !resp.Success {
			return fmt.Errorf("close order failed: %s", resp.Message)
		}

		logger.WithFields(map[string]interface{}{
			"order_no":    order.OrderNo,
			"pay_channel": order.PayChannel,
			"message":     resp.Message,
		}).Info("Payment platform order closed successfully")

		return nil
	}, 2, 1*time.Second) // 最多重试2次，基础延迟1秒

	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"order_no":    order.OrderNo,
			"pay_channel": order.PayChannel,
		}).Error("Failed to close payment platform order after retries")
		return err
	}

	return nil
}

// syncOrderStatus 同步订单状态
func (s *paymentService) syncOrderStatus(ctx context.Context, order *model.PaymentOrder) {
	queryReq := &payment.QueryOrderRequest{
		OrderNo: order.OrderNo,
		Channel: order.PayChannel,
	}

	resp, err := s.paymentClient.QueryOrder(queryReq)
	if err != nil {
		logger.WithError(err).WithField("order_no", order.OrderNo).Error("Failed to query order status")
		return
	}

	var newStatus string
	var transactionID string
	var paidAt *types.Time

	switch resp.Status {
	case "TRADE_SUCCESS", "SUCCESS", "TRADE_FINISHED":
		newStatus = model.OrderStatusPaid
		transactionID = resp.TransactionID
		if resp.PaidAt != "" {
			if parsedTime, err := time.Parse("2006-01-02 15:04:05", resp.PaidAt); err == nil {
				typedPaidAt := types.NewTime(parsedTime)
				paidAt = &typedPaidAt
			}
		}
	case "TRADE_CLOSED", "CLOSED":
		newStatus = model.OrderStatusClosed
	default:
		return // 状态未变化
	}

	if newStatus != order.Status {
		// 如果订单状态变为已支付，执行完整的支付成功处理逻辑
		if newStatus == model.OrderStatusPaid {
			s.handlePaymentSuccessFromQuery(ctx, order, transactionID, paidAt)
		} else {
			// 其他状态变化只更新订单状态
			order.Status = newStatus
			if err := s.paymentOrderRepo.Update(ctx, order); err != nil {
				logger.WithError(err).WithField("order_no", order.OrderNo).Error("Failed to update order status")
			}
		}
	}
}

// generateOrderNo 生成订单号
func generateOrderNo() string {
	// 格式：时间戳 + 随机数
	timestamp := types.Now().Format("20060102150405")

	// 生成6位随机数
	bytes := make([]byte, 3)
	rand.Read(bytes)
	randomStr := hex.EncodeToString(bytes)

	return timestamp + randomStr
}

// createPaymentOrder 创建支付平台订单
func (s *paymentService) createPaymentOrder(ctx context.Context, order *model.PaymentOrder, req *model.CreateOrderRequest) (model.JSON, error) {
	// 构建回调URL
	notifyURL := req.NotifyURL
	if notifyURL == "" {
		// 如果请求中没有提供回调URL，使用默认的
		notifyURL = s.buildNotifyURL(order.PayChannel)
	}

	// 构建支付请求
	paymentReq := &payment.NativeOrderRequest{
		OrderNo:       order.OrderNo,
		Subject:       order.Subject,
		Body:          order.Body,
		Amount:        order.TotalAmount,
		Channel:       order.PayChannel,
		NotifyURL:     notifyURL,
		ExpireMinutes: req.ExpireMinutes,
	}

	// 调用支付客户端创建订单（使用重试机制）
	var resp *payment.NativeOrderResponse
	err := s.retryWithBackoff(ctx, func() error {
		var createErr error
		resp, createErr = s.paymentClient.CreateNativeOrder(paymentReq)
		return createErr
	}, 2, 1*time.Second) // 最多重试2次，基础延迟1秒

	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"order_no":    order.OrderNo,
			"pay_channel": order.PayChannel,
			"amount":      order.TotalAmount,
		}).Error("Failed to create payment order after retries")
		return nil, err
	}

	// 构建支付信息
	payInfo := model.JSON{
		"qr_code":   resp.QRCode,
		"prepay_id": resp.PrepayID,
		"trade_no":  resp.TradeNo,
	}

	// 根据不同支付渠道添加特定信息
	switch order.PayChannel {
	case "wechat":
		if resp.QRCode != "" {
			payInfo["code_url"] = resp.QRCode
		}
	case "alipay":
		if resp.QRCode != "" {
			payInfo["qr_code"] = resp.QRCode
		}
	}

	logger.WithFields(map[string]interface{}{
		"order_no":    order.OrderNo,
		"pay_channel": order.PayChannel,
		"qr_code":     resp.QRCode != "",
		"prepay_id":   resp.PrepayID,
	}).Info("Payment order created successfully")

	return payInfo, nil
}

// isRetryableError 判断是否为可重试的错误
func (s *paymentService) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// 网络相关错误通常可以重试
	errStr := err.Error()
	retryableErrors := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"network is unreachable",
		"temporary failure",
		"service unavailable",
		"gateway timeout",
		"bad gateway",
	}

	for _, retryableErr := range retryableErrors {
		if contains(errStr, retryableErr) {
			return true
		}
	}

	return false
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					findSubstring(s, substr)))
}

// findSubstring 在字符串中查找子字符串
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// retryWithBackoff 使用指数退避重试
func (s *paymentService) retryWithBackoff(ctx context.Context, operation func() error, maxRetries int, baseDelay time.Duration) error {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避：baseDelay * 2^(attempt-1)
			delay := baseDelay * time.Duration(1<<(attempt-1))
			if delay > 30*time.Second {
				delay = 30 * time.Second // 最大延迟30秒
			}

			logger.WithFields(map[string]interface{}{
				"attempt": attempt,
				"delay":   delay.String(),
				"error":   lastErr.Error(),
			}).Warn("Retrying operation after error")

			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(delay):
			}
		}

		err := operation()
		if err == nil {
			if attempt > 0 {
				logger.WithField("attempts", attempt+1).Info("Operation succeeded after retry")
			}
			return nil
		}

		lastErr = err

		// 如果不是可重试的错误，直接返回
		if !s.isRetryableError(err) {
			logger.WithFields(map[string]interface{}{
				"error":   err.Error(),
				"attempt": attempt + 1,
			}).Debug("Error is not retryable, stopping retry attempts")
			break
		}
	}

	logger.WithFields(map[string]interface{}{
		"max_retries": maxRetries,
		"final_error": lastErr.Error(),
	}).Error("Operation failed after all retry attempts")

	return fmt.Errorf("operation failed after %d attempts: %w", maxRetries+1, lastErr)
}

// handlePaymentSuccess 处理支付成功通知
func (s *paymentService) handlePaymentSuccess(ctx context.Context, notifyData *payment.NotifyData) error {
	// 使用分布式锁防止重复处理
	lockKey := lock.PaymentLockKey(notifyData.OrderNo)
	return lock.WithLock(ctx, s.redis, lockKey, 30*time.Second, func() error {
		return s.doHandlePaymentSuccess(ctx, notifyData)
	})
}

// doHandlePaymentSuccess 实际处理支付成功的逻辑（在锁保护下执行）
func (s *paymentService) doHandlePaymentSuccess(ctx context.Context, notifyData *payment.NotifyData) error {
	// 根据订单号查询订单
	order, err := s.paymentOrderRepo.GetByOrderNo(ctx, notifyData.OrderNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.WithField("order_no", notifyData.OrderNo).Warn("Order not found for payment notification")
			return fmt.Errorf("order not found: %s", notifyData.OrderNo)
		}
		return fmt.Errorf("failed to get order: %w", err)
	}

	// 检查订单状态，避免重复处理
	if order.Status == model.OrderStatusPaid {
		logger.WithField("order_no", notifyData.OrderNo).Info("Order already paid, skipping")
		return nil
	}

	// 检查订单是否已过期
	if order.IsExpired() {
		logger.WithField("order_no", notifyData.OrderNo).Warn("Order expired, but received payment notification")
		// 过期订单收到支付通知，仍然需要更新状态，但可能需要特殊处理
	}

	// 验证金额是否匹配
	if !notifyData.Amount.Equal(order.TotalAmount) {
		logger.WithFields(map[string]interface{}{
			"order_no":      notifyData.OrderNo,
			"order_amount":  order.TotalAmount,
			"notify_amount": notifyData.Amount,
		}).Error("Payment amount mismatch")
		return fmt.Errorf("payment amount mismatch: order=%s, notify=%s",
			order.TotalAmount.String(), notifyData.Amount.String())
	}

	// 更新订单状态
	now := types.Now()
	order.Status = model.OrderStatusPaid
	order.OutTradeNo = notifyData.TransactionID
	order.PaidAt = &now

	// 更新付款方信息
	if order.PayerInfo == nil {
		order.PayerInfo = make(model.JSON)
	}
	order.PayerInfo["transaction_id"] = notifyData.TransactionID
	order.PayerInfo["paid_at"] = notifyData.PaidAt

	// 保存订单更新
	if err := s.paymentOrderRepo.Update(ctx, order); err != nil {
		logger.WithError(err).WithField("order_no", notifyData.OrderNo).Error("Failed to update order status")
		return fmt.Errorf("failed to update order: %w", err)
	}

	// 创建支付记录
	paymentRecord := &model.PaymentRecord{
		OrderNo:       order.OrderNo,
		OutTradeNo:    order.OrderNo, // 商户订单号
		TradeNo:       notifyData.TransactionID,
		PayChannel:    order.PayChannel,
		TotalAmount:   notifyData.Amount,
		ReceiptAmount: notifyData.Amount, // 实收金额，暂时等于订单金额
		TradeTime:     now,
	}

	if err := s.paymentRecordRepo.Create(ctx, paymentRecord); err != nil {
		logger.WithError(err).WithField("order_no", notifyData.OrderNo).Error("Failed to create payment record")
		// 支付记录创建失败不影响主流程，只记录日志
	}

	logger.WithFields(map[string]interface{}{
		"order_no":       notifyData.OrderNo,
		"transaction_id": notifyData.TransactionID,
		"amount":         notifyData.Amount,
		"pay_channel":    order.PayChannel,
	}).Info("Payment processed successfully")

	// 移除延迟队列中的任务（订单已支付成功）
	if s.queueService != nil {
		// 移除订单过期任务
		if err := s.queueService.RemoveOrderExpireTask(notifyData.OrderNo); err != nil {
			logger.WithError(err).WithField("order_no", notifyData.OrderNo).Error("Failed to remove order expire task")
		}

		// 移除所有轮询任务（可能有多个）
		for i := 1; i <= 5; i++ {
			if err := s.queueService.RemoveOrderPollTask(notifyData.OrderNo, i); err != nil {
				logger.WithError(err).WithFields(map[string]interface{}{
					"order_no":   notifyData.OrderNo,
					"poll_count": i,
				}).Error("Failed to remove order poll task")
			}
		}

		logger.WithField("order_no", notifyData.OrderNo).Debug("Removed order tasks from delay queues")
	}

	// 调用机器人账户服务进行充值
	if err := s.notifyRobotService(ctx, order, notifyData.Amount); err != nil {
		logger.WithError(err).WithField("order_no", notifyData.OrderNo).Error("Failed to notify robot service")
		// 机器人服务调用失败不影响主流程，只记录日志
	}

	return nil
}

// handlePaymentSuccessFromQuery 处理主动查询发现的支付成功
func (s *paymentService) handlePaymentSuccessFromQuery(ctx context.Context, order *model.PaymentOrder, transactionID string, paidAt *types.Time) {
	// 使用分布式锁防止重复处理
	lockKey := lock.PaymentLockKey(order.OrderNo)
	err := lock.WithLock(ctx, s.redis, lockKey, 30*time.Second, func() error {
		return s.doHandlePaymentSuccessFromQuery(ctx, order, transactionID, paidAt)
	})
	if err != nil {
		logger.WithError(err).WithField("order_no", order.OrderNo).Error("Failed to process payment success from query")
	}
}

// doHandlePaymentSuccessFromQuery 实际处理主动查询发现的支付成功（在锁保护下执行）
func (s *paymentService) doHandlePaymentSuccessFromQuery(ctx context.Context, order *model.PaymentOrder, transactionID string, paidAt *types.Time) error {
	// 检查订单状态，避免重复处理
	if order.Status == model.OrderStatusPaid {
		logger.WithField("order_no", order.OrderNo).Info("Order already paid, skipping query processing")
		return nil
	}

	logger.WithFields(map[string]interface{}{
		"order_no":       order.OrderNo,
		"transaction_id": transactionID,
		"source":         "QUERY",
	}).Info("Processing payment success from query")

	// 更新订单状态
	now := types.Now()
	order.Status = model.OrderStatusPaid
	order.OutTradeNo = transactionID
	if paidAt != nil {
		order.PaidAt = paidAt
	} else {
		order.PaidAt = &now
	}

	// 更新付款方信息
	if order.PayerInfo == nil {
		order.PayerInfo = make(model.JSON)
	}
	order.PayerInfo["transaction_id"] = transactionID
	order.PayerInfo["source"] = "QUERY"
	if paidAt != nil {
		order.PayerInfo["paid_at"] = paidAt.Format("2006-01-02 15:04:05")
	}

	// 保存订单更新
	if err := s.paymentOrderRepo.Update(ctx, order); err != nil {
		logger.WithError(err).WithField("order_no", order.OrderNo).Error("Failed to update order status from query")
		return err
	}

	// 创建支付记录
	paymentRecord := &model.PaymentRecord{
		OrderNo:       order.OrderNo,
		OutTradeNo:    order.OrderNo, // 商户订单号
		TradeNo:       transactionID,
		PayChannel:    order.PayChannel,
		TotalAmount:   order.TotalAmount,
		ReceiptAmount: order.TotalAmount, // 实收金额，暂时等于订单金额
		TradeTime:     now,
	}

	if err := s.paymentRecordRepo.Create(ctx, paymentRecord); err != nil {
		logger.WithError(err).WithField("order_no", order.OrderNo).Error("Failed to create payment record from query")
		// 支付记录创建失败不影响主流程，只记录日志
	}

	// 记录通知日志（标记为主动查询来源）
	s.recordNotifyLogWithType(ctx, order.OrderNo, order.PayChannel,
		fmt.Sprintf("Query result: status=PAID, transaction_id=%s", transactionID),
		"SUCCESS", "", "QUERY", "PAYMENT")

	logger.WithFields(map[string]interface{}{
		"order_no":       order.OrderNo,
		"transaction_id": transactionID,
		"amount":         order.TotalAmount,
		"pay_channel":    order.PayChannel,
		"source":         "QUERY",
	}).Info("Payment processed successfully from query")

	// 移除延迟队列中的任务（订单已支付成功）
	if s.queueService != nil {
		// 移除订单过期任务
		if err := s.queueService.RemoveOrderExpireTask(order.OrderNo); err != nil {
			logger.WithError(err).WithField("order_no", order.OrderNo).Error("Failed to remove order expire task")
		}

		// 移除所有轮询任务（可能有多个）
		for i := 1; i <= 5; i++ {
			if err := s.queueService.RemoveOrderPollTask(order.OrderNo, i); err != nil {
				logger.WithError(err).WithFields(map[string]interface{}{
					"order_no":   order.OrderNo,
					"poll_count": i,
				}).Error("Failed to remove order poll task")
			}
		}

		logger.WithField("order_no", order.OrderNo).Debug("Removed order tasks from delay queues")
	}

	// 调用机器人账户服务进行充值
	if err := s.notifyRobotService(ctx, order, order.TotalAmount); err != nil {
		logger.WithError(err).WithField("order_no", order.OrderNo).Error("Failed to notify robot service from query")
		// 机器人服务调用失败不影响主流程，只记录日志
	}

	return nil
}

// handleRefundSuccess 处理退款成功通知
func (s *paymentService) handleRefundSuccess(ctx context.Context, notifyData *payment.NotifyData) error {
	// 根据退款单号查询退款订单
	refundOrder, err := s.refundOrderRepo.GetByRefundNo(ctx, notifyData.RefundNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.WithField("refund_no", notifyData.RefundNo).Warn("Refund order not found for refund notification")
			return fmt.Errorf("refund order not found: %s", notifyData.RefundNo)
		}
		return fmt.Errorf("failed to get refund order: %w", err)
	}

	// 检查退款订单状态，避免重复处理
	if refundOrder.Status == model.RefundStatusSuccess {
		logger.WithField("refund_no", notifyData.RefundNo).Info("Refund order already processed, skipping")
		return nil
	}

	// 根据订单号查询原订单
	order, err := s.paymentOrderRepo.GetByOrderNo(ctx, notifyData.OrderNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.WithField("order_no", notifyData.OrderNo).Warn("Order not found for refund notification")
			return fmt.Errorf("order not found: %s", notifyData.OrderNo)
		}
		return fmt.Errorf("failed to get order: %w", err)
	}

	// 检查订单状态
	if order.Status != model.OrderStatusPaid {
		logger.WithFields(map[string]interface{}{
			"order_no":     notifyData.OrderNo,
			"order_status": order.Status,
		}).Warn("Order is not in paid status for refund notification")
	}

	// 更新退款订单状态
	now := types.Now()
	refundOrder.Status = model.RefundStatusSuccess
	refundOrder.SuccessTime = &now

	// 保存退款订单更新
	if err := s.refundOrderRepo.Update(ctx, refundOrder); err != nil {
		logger.WithError(err).WithField("refund_no", notifyData.RefundNo).Error("Failed to update refund order status")
		return fmt.Errorf("failed to update refund order: %w", err)
	}

	// 更新原订单状态为已退款
	order.Status = model.OrderStatusRefunded

	// 保存原订单更新
	if err := s.paymentOrderRepo.Update(ctx, order); err != nil {
		logger.WithError(err).WithField("order_no", notifyData.OrderNo).Error("Failed to update order status for refund")
		return fmt.Errorf("failed to update order: %w", err)
	}

	// 创建退款记录
	refundRecord := &model.RefundRecord{
		RefundNo:           refundOrder.RefundNo,
		OutRefundNo:        refundOrder.OutRefundNo,
		RefundTradeNo:      notifyData.TransactionID,
		PayChannel:         order.PayChannel,
		OrderNo:            order.OrderNo,
		OutTradeNo:         order.OutTradeNo,
		RefundAmount:       refundOrder.RefundAmount,
		ActualRefundAmount: notifyData.Amount, // 实际退款金额
		RefundTime:         now,
	}

	if err := s.refundRecordRepo.Create(ctx, refundRecord); err != nil {
		logger.WithError(err).WithField("refund_no", notifyData.RefundNo).Error("Failed to create refund record")
		// 退款记录创建失败不影响主流程，只记录日志
	}

	// 调用机器人账户服务进行退款
	if err := s.notifyRobotRefundService(ctx, order, refundOrder, notifyData.Amount); err != nil {
		logger.WithError(err).WithField("refund_no", notifyData.RefundNo).Error("Failed to notify robot service for refund")
		// 机器人服务调用失败不影响主流程，只记录日志
	}

	logger.WithFields(map[string]interface{}{
		"refund_no":      notifyData.RefundNo,
		"order_no":       notifyData.OrderNo,
		"transaction_id": notifyData.TransactionID,
		"refund_amount":  notifyData.Amount,
		"pay_channel":    order.PayChannel,
	}).Info("Refund processed successfully")

	return nil
}

// CloseExpiredOrder 关闭单个过期订单
func (s *paymentService) CloseExpiredOrder(ctx context.Context, orderNo string) error {
	// 获取订单
	order, err := s.paymentOrderRepo.GetByOrderNo(ctx, orderNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.WithField("order_no", orderNo).Debug("Order not found for expire task")
			return nil // 订单不存在，视为成功
		}
		return fmt.Errorf("failed to get order: %w", err)
	}

	// 检查订单状态
	if order.Status != model.OrderStatusWaitingPay {
		logger.WithFields(map[string]interface{}{
			"order_no": orderNo,
			"status":   order.Status,
		}).Debug("Order is not in waiting pay status, skipping expire")
		return nil
	}

	// 检查是否真的过期
	if !order.IsExpired() {
		logger.WithField("order_no", orderNo).Debug("Order is not expired yet")
		return nil
	}

	// 先尝试关闭支付平台订单
	if err := s.closePaymentPlatformOrder(ctx, order); err != nil {
		logger.WithError(err).WithField("order_no", orderNo).
			Warn("Failed to close payment platform order, continuing with local close")
	}

	// 关闭本地订单
	order.Status = model.OrderStatusClosed
	if err := s.paymentOrderRepo.Update(ctx, order); err != nil {
		return fmt.Errorf("failed to close expired order: %w", err)
	}

	logger.WithField("order_no", orderNo).Info("Successfully closed expired order")
	return nil
}

// PollOrderStatus 轮询订单状态
func (s *paymentService) PollOrderStatus(ctx context.Context, orderNo string, pollCount int) error {
	// 获取订单
	order, err := s.paymentOrderRepo.GetByOrderNo(ctx, orderNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.WithField("order_no", orderNo).Debug("Order not found for poll task")
			return nil // 订单不存在，视为成功
		}
		return fmt.Errorf("failed to get order: %w", err)
	}

	// 检查订单状态
	if order.Status != model.OrderStatusWaitingPay {
		logger.WithFields(map[string]interface{}{
			"order_no":   orderNo,
			"status":     order.Status,
			"poll_count": pollCount,
		}).Debug("Order is not in waiting pay status, stopping poll")
		return nil
	}

	// 检查是否过期
	if order.IsExpired() {
		logger.WithField("order_no", orderNo).Debug("Order is expired, will be handled by expire task")
		return nil
	}

	logger.WithFields(map[string]interface{}{
		"order_no":   orderNo,
		"poll_count": pollCount,
	}).Info("Polling order status")

	// 同步订单状态
	s.syncOrderStatus(ctx, order)

	// 重新获取订单状态
	updatedOrder, err := s.paymentOrderRepo.GetByOrderNo(ctx, orderNo)
	if err != nil {
		return fmt.Errorf("failed to get updated order: %w", err)
	}

	// 如果订单仍然是待支付状态，安排下次轮询
	if updatedOrder.Status == model.OrderStatusWaitingPay && !updatedOrder.IsExpired() {
		nextPollCount := pollCount + 1
		maxPollCount := 5 // 最多轮询5次

		if nextPollCount <= maxPollCount {
			// 计算下次轮询时间（递增间隔）
			var nextPollDelay time.Duration
			switch nextPollCount {
			case 2:
				nextPollDelay = 2 * time.Minute
			case 3:
				nextPollDelay = 3 * time.Minute
			case 4:
				nextPollDelay = 5 * time.Minute
			case 5:
				nextPollDelay = 5 * time.Minute
			default:
				nextPollDelay = 1 * time.Minute
			}

			nextPollTime := time.Now().Add(nextPollDelay)
			if err := s.queueService.AddOrderPollTask(orderNo, nextPollTime, nextPollCount); err != nil {
				logger.WithError(err).WithField("order_no", orderNo).Error("Failed to schedule next poll task")
			} else {
				logger.WithFields(map[string]interface{}{
					"order_no":        orderNo,
					"next_poll_count": nextPollCount,
					"next_poll_time":  nextPollTime,
				}).Debug("Scheduled next poll task")
			}
		} else {
			logger.WithFields(map[string]interface{}{
				"order_no":   orderNo,
				"poll_count": pollCount,
			}).Info("Reached max poll count, stopping poll")
		}
	}

	return nil
}

// CloseExpiredRefund 关闭过期退款
func (s *paymentService) CloseExpiredRefund(ctx context.Context, refundNo string) error {
	// 获取退款订单
	refundOrder, err := s.refundOrderRepo.GetByRefundNo(ctx, refundNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.WithField("refund_no", refundNo).Debug("Refund order not found for expire task")
			return nil // 退款订单不存在，视为成功
		}
		return fmt.Errorf("failed to get refund order: %w", err)
	}

	// 检查退款状态
	if refundOrder.Status != model.RefundStatusProcessing {
		logger.WithFields(map[string]interface{}{
			"refund_no": refundNo,
			"status":    refundOrder.Status,
		}).Debug("Refund order is not in processing status, skipping expire")
		return nil
	}

	logger.WithField("refund_no", refundNo).Info("Closing expired refund order")

	// 更新退款状态为失败
	refundOrder.Status = model.RefundStatusFailed

	if err := s.refundOrderRepo.Update(ctx, refundOrder); err != nil {
		logger.WithError(err).WithField("refund_no", refundNo).Error("Failed to update expired refund order")
		return fmt.Errorf("failed to update refund order: %w", err)
	}

	logger.WithField("refund_no", refundNo).Info("Expired refund order closed successfully")
	return nil
}

// PollRefundStatus 轮询退款状态
func (s *paymentService) PollRefundStatus(ctx context.Context, refundNo string, pollCount int) error {
	// 获取退款订单
	refundOrder, err := s.refundOrderRepo.GetByRefundNo(ctx, refundNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.WithField("refund_no", refundNo).Debug("Refund order not found for poll task")
			return nil // 退款订单不存在，视为成功
		}
		return fmt.Errorf("failed to get refund order: %w", err)
	}

	// 检查退款状态
	if refundOrder.Status != model.RefundStatusProcessing {
		logger.WithFields(map[string]interface{}{
			"refund_no":  refundNo,
			"status":     refundOrder.Status,
			"poll_count": pollCount,
		}).Debug("Refund order is not in processing status, stopping poll")
		return nil
	}

	logger.WithFields(map[string]interface{}{
		"refund_no":  refundNo,
		"poll_count": pollCount,
	}).Info("Polling refund status")

	// 同步退款状态
	s.syncRefundStatus(ctx, refundOrder)

	// 重新获取退款状态
	updatedRefund, err := s.refundOrderRepo.GetByRefundNo(ctx, refundNo)
	if err != nil {
		return fmt.Errorf("failed to get updated refund order: %w", err)
	}

	// 如果退款仍然是处理中状态，安排下次轮询
	if updatedRefund.Status == model.RefundStatusProcessing {
		nextPollCount := pollCount + 1
		maxPollCount := 5 // 最多轮询5次

		if nextPollCount <= maxPollCount {
			// 计算下次轮询时间（递增间隔）
			var nextPollDelay time.Duration
			switch nextPollCount {
			case 2:
				nextPollDelay = 2 * time.Minute
			case 3:
				nextPollDelay = 3 * time.Minute
			case 4:
				nextPollDelay = 5 * time.Minute
			case 5:
				nextPollDelay = 5 * time.Minute
			default:
				nextPollDelay = 1 * time.Minute
			}

			nextPollTime := time.Now().Add(nextPollDelay)
			if err := s.queueService.AddRefundPollTask(refundNo, nextPollTime, nextPollCount); err != nil {
				logger.WithError(err).WithField("refund_no", refundNo).Error("Failed to schedule next refund poll task")
			} else {
				logger.WithFields(map[string]interface{}{
					"refund_no":       refundNo,
					"next_poll_count": nextPollCount,
					"next_poll_time":  nextPollTime,
				}).Debug("Scheduled next refund poll task")
			}
		} else {
			logger.WithField("refund_no", refundNo).Info("Max refund poll count reached, stopping polling")
		}
	}

	return nil
}

// syncRefundStatus 同步退款状态
func (s *paymentService) syncRefundStatus(ctx context.Context, refundOrder *model.RefundOrder) {
	// 获取原订单信息
	order, err := s.paymentOrderRepo.GetByOrderNo(ctx, refundOrder.OrderNo)
	if err != nil {
		logger.WithError(err).WithField("refund_no", refundOrder.RefundNo).Error("Failed to get original order for refund status sync")
		return
	}

	// TODO: 这里应该调用支付平台的退款查询接口
	// 目前暂时模拟查询结果，实际应该根据支付平台的API实现
	logger.WithFields(map[string]interface{}{
		"refund_no":   refundOrder.RefundNo,
		"order_no":    order.OrderNo,
		"pay_channel": order.PayChannel,
	}).Debug("Syncing refund status (query not implemented yet)")

	// 模拟查询结果 - 在实际实现中，这里应该是真实的查询结果
	// queryResp, err := s.paymentClient.QueryRefund(&payment.QueryRefundRequest{
	//     RefundNo: refundOrder.RefundNo,
	//     Channel:  order.PayChannel,
	// })
	// if err != nil {
	//     logger.WithError(err).WithField("refund_no", refundOrder.RefundNo).Error("Failed to query refund status")
	//     return
	// }

	// 这里暂时不做状态更新，等待支付平台的退款查询接口实现
	// 如果发现退款成功，应该执行以下逻辑：
	// if queryResp.Status == "SUCCESS" {
	//     s.handleRefundSuccessFromQuery(ctx, order, refundOrder, queryResp.RefundAmount)
	// }
}

// notifyRobotRefundService 通知机器人账户服务进行退款
func (s *paymentService) notifyRobotRefundService(ctx context.Context, order *model.PaymentOrder, refundOrder *model.RefundOrder, refundAmount decimal.Decimal) error {
	// 检查是否配置了机器人服务
	if s.robotClient == nil {
		logger.Info("Robot client not configured, skipping robot refund service notification")
		return nil
	}

	// 获取deviceSN（appUserID）
	deviceSN := order.AppUserID
	if deviceSN == "" {
		logger.WithField("refund_no", refundOrder.RefundNo).Warn("AppUserID is empty, cannot notify robot refund service")
		return fmt.Errorf("appUserID is empty")
	}

	// 构建退款请求
	refundReq := &robot.RefundRequest{
		Amount:          refundAmount,
		OriginalOrderID: order.OrderNo,
		RefundOrderID:   refundOrder.RefundNo,
		RefundReason:    refundOrder.RefundReason,
	}

	logger.WithFields(map[string]interface{}{
		"refund_no":         refundOrder.RefundNo,
		"original_order_no": order.OrderNo,
		"device_sn":         deviceSN,
		"refund_amount":     refundAmount,
		"refund_reason":     refundOrder.RefundReason,
	}).Info("Calling robot service for refund")

	// 调用机器人退款服务
	resp, err := s.robotClient.Refund(ctx, deviceSN, refundReq)

	// 记录业务回调日志
	status := "SUCCESS"
	errorMsg := ""
	responseBody := ""

	if err != nil {
		status = "FAILED"
		errorMsg = err.Error()
	} else if resp != nil {
		respBytes, _ := json.Marshal(resp)
		responseBody = string(respBytes)
		if resp.Code != 200 {
			status = "FAILED"
			errorMsg = resp.Message
		}
	}

	// 构建请求体用于日志记录
	requestBytes, _ := json.Marshal(refundReq)
	requestBody := string(requestBytes)

	// 记录到notify_logs
	notifyLog := &model.NotifyLog{
		OrderNo:      order.OrderNo,
		AppID:        &order.AppID,
		NotifyType:   "REFUND",
		NotifySource: "APP",
		NotifyURL:    s.robotClient.GetBaseURL() + "/api/v1/robot/refund",
		RequestBody:  requestBody,
		ResponseCode: 200, // HTTP状态码
		ResponseBody: responseBody,
		Status:       status,
		ErrorMsg:     errorMsg,
		CreatedAt:    types.Now(),
	}

	if logErr := s.notifyLogRepo.Create(ctx, notifyLog); logErr != nil {
		logger.WithError(logErr).WithField("refund_no", refundOrder.RefundNo).Error("Failed to create robot refund service notify log")
	}

	if err != nil {
		return fmt.Errorf("robot refund service call failed: %w", err)
	}

	logger.WithFields(map[string]interface{}{
		"refund_no":         refundOrder.RefundNo,
		"original_order_no": order.OrderNo,
		"device_sn":         deviceSN,
		"refund_amount":     refundAmount,
		"robot_code":        resp.Code,
		"robot_message":     resp.Message,
		"remaining_balance": resp.Data.Balance,
	}).Info("Robot service refund completed successfully")

	return nil
}
