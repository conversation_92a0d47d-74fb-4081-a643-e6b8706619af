package handler

import (
	"errors"
	"io"
	"net/http"
	"strconv"

	"pay-core/internal/middleware"
	"pay-core/internal/model"
	"pay-core/internal/repository"
	"pay-core/internal/service"
	"pay-core/pkg/logger"

	"github.com/gin-gonic/gin"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService service.PaymentService
	appRepo        repository.AppRepository
}

// NewPaymentHandler 创建支付处理器
func NewPaymentHandler(paymentService service.PaymentService, appRepo repository.AppRepository) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
		appRepo:        appRepo,
	}
}

// getAppIDFromContext 从上下文中获取AppID
func (h *PaymentHandler) getAppIDFromContext(c *gin.Context) (uint64, error) {
	appCode := middleware.GetAppCode(c)
	if appCode == "" {
		return 0, errors.New("missing app code")
	}

	app, err := h.appRepo.GetByAppCode(c.Request.Context(), appCode)
	if err != nil {
		return 0, errors.New("invalid app code")
	}

	if !app.Status {
		return 0, errors.New("app is disabled")
	}

	return app.ID, nil
}

// CreateOrder 创建支付订单
// @Summary 创建支付订单
// @Description 为应用创建新的支付订单
// @Tags 支付
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body model.CreateOrderRequest true "订单信息"
// @Success 200 {object} Response{data=model.CreateOrderResponse}
// @Failure 400 {object} Response
// @Router /api/v1/orders [post]
func (h *PaymentHandler) CreateOrder(c *gin.Context) {
	var req model.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ValidationErrorResponse("Invalid request parameters"))
		return
	}

	// 从上下文中获取AppID
	appID, err := h.getAppIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse(401, err.Error()))
		return
	}

	// 设置AppUserId到请求中
	if req.AppUserID == "" {
		req.AppUserID = middleware.GetAppUserId(c)
	}

	order, err := h.paymentService.CreateOrder(c.Request.Context(), appID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(400, err.Error()))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(order))
}

// GetOrder 获取订单信息
// @Summary 获取订单信息
// @Description 根据订单号获取订单详细信息
// @Tags 支付
// @Produce json
// @Security ApiKeyAuth
// @Param order_no path string true "订单号"
// @Success 200 {object} Response{data=model.PaymentOrder}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /api/v1/orders/{order_no} [get]
func (h *PaymentHandler) GetOrder(c *gin.Context) {
	orderNo := c.Param("order_no")

	// 从上下文中获取AppID
	appID, err := h.getAppIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse(401, err.Error()))
		return
	}

	order, err := h.paymentService.GetOrder(c.Request.Context(), appID, orderNo)
	if err != nil {
		if err.Error() == "order not found" {
			c.JSON(http.StatusNotFound, NotFoundResponse("Order not found"))
		} else {
			c.JSON(http.StatusBadRequest, ErrorResponse(400, err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(order))
}

// QueryOrder 查询订单状态
// @Summary 查询订单状态
// @Description 根据业务订单号或系统订单号查询订单状态
// @Tags 支付
// @Produce json
// @Security ApiKeyAuth
// @Param order_no query string false "系统订单号"
// @Param app_order_no query string false "业务订单号"
// @Param out_trade_no query string false "第三方交易号"
// @Success 200 {object} Response{data=model.PaymentOrder}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /api/v1/orders/query [get]
func (h *PaymentHandler) QueryOrder(c *gin.Context) {
	var req model.QueryOrderRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, ValidationErrorResponse("Invalid query parameters"))
		return
	}

	// 从上下文中获取AppID
	appID, err := h.getAppIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse(401, err.Error()))
		return
	}

	order, err := h.paymentService.QueryOrder(c.Request.Context(), appID, &req)
	if err != nil {
		if err.Error() == "order not found" {
			c.JSON(http.StatusNotFound, NotFoundResponse("Order not found"))
		} else {
			c.JSON(http.StatusBadRequest, ErrorResponse(400, err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(order))
}

// Notify 支付回调通知
// @Summary 支付回调通知
// @Description 接收支付渠道的异步回调通知
// @Tags 支付
// @Accept x-www-form-urlencoded
// @Produce json
// @Param channel path string true "支付渠道"
// @Success 200 {string} string "SUCCESS"
// @Failure 400 {string} string "FAIL"
// @Router /notify/{channel} [post]
func (h *PaymentHandler) Notify(c *gin.Context) {
	channel := c.Param("channel")

	// 记录请求开始
	logger.WithFields(map[string]interface{}{
		"channel":    channel,
		"method":     c.Request.Method,
		"url":        c.Request.URL.String(),
		"user_agent": c.Request.UserAgent(),
		"remote_ip":  c.ClientIP(),
		"headers":    h.sanitizeHeaders(c.Request.Header),
	}).Info("Received payment notification")

	// 验证渠道参数
	if channel == "" {
		logger.Error("Missing channel parameter in payment notification")
		c.String(http.StatusBadRequest, "FAIL")
		return
	}

	// 验证支持的渠道
	supportedChannels := []string{"wechat", "alipay", "qq"}
	isSupported := false
	for _, supported := range supportedChannels {
		if channel == supported {
			isSupported = true
			break
		}
	}
	if !isSupported {
		logger.WithField("channel", channel).Error("Unsupported payment channel")
		c.String(http.StatusBadRequest, "FAIL")
		return
	}

	// 处理回调
	if err := h.paymentService.HandleNotify(c.Request.Context(), channel, c.Request); err != nil {
		logger.WithError(err).WithField("channel", channel).Error("Failed to handle payment notification")
		c.String(http.StatusBadRequest, "FAIL")
		return
	}

	logger.WithField("channel", channel).Info("Payment notification processed successfully")
	c.String(http.StatusOK, "SUCCESS")
}

// RefundNotify 退款回调通知
// @Summary 退款回调通知
// @Description 接收支付渠道的退款异步回调通知
// @Tags 支付
// @Accept x-www-form-urlencoded
// @Produce json
// @Param channel path string true "支付渠道"
// @Success 200 {string} string "SUCCESS"
// @Failure 400 {string} string "FAIL"
// @Router /notify/{channel}/refund [post]
func (h *PaymentHandler) RefundNotify(c *gin.Context) {
	channel := c.Param("channel")

	// 记录请求开始
	logger.WithFields(map[string]interface{}{
		"channel":    channel,
		"method":     c.Request.Method,
		"url":        c.Request.URL.String(),
		"user_agent": c.Request.UserAgent(),
		"remote_ip":  c.ClientIP(),
		"headers":    h.sanitizeHeaders(c.Request.Header),
		"type":       "REFUND",
	}).Info("Received refund notification")

	// 验证渠道参数
	if channel == "" {
		logger.Error("Missing channel parameter in refund notification")
		c.String(http.StatusBadRequest, "FAIL")
		return
	}

	// 验证支持的渠道
	supportedChannels := []string{"wechat", "alipay", "qq"}
	isSupported := false
	for _, supported := range supportedChannels {
		if channel == supported {
			isSupported = true
			break
		}
	}
	if !isSupported {
		logger.WithField("channel", channel).Error("Unsupported refund channel")
		c.String(http.StatusBadRequest, "FAIL")
		return
	}

	// 处理回调
	if err := h.paymentService.HandleNotify(c.Request.Context(), channel, c.Request); err != nil {
		logger.WithError(err).WithField("channel", channel).Error("Failed to handle refund notification")
		c.String(http.StatusBadRequest, "FAIL")
		return
	}

	logger.WithField("channel", channel).Info("Refund notification processed successfully")
	c.String(http.StatusOK, "SUCCESS")
}

// RefundOrder 申请退款
// @Summary 申请退款
// @Description 为已支付的订单申请退款
// @Tags 支付
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param order_no path string true "订单号"
// @Param request body model.RefundOrderRequest true "退款信息"
// @Success 200 {object} Response{data=model.RefundOrder}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /api/v1/orders/{order_no}/refund [post]
func (h *PaymentHandler) RefundOrder(c *gin.Context) {
	orderNo := c.Param("order_no")

	var req model.RefundOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ValidationErrorResponse("Invalid request parameters"))
		return
	}

	// 从上下文中获取AppID
	appID, err := h.getAppIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse(401, err.Error()))
		return
	}

	refundOrder, err := h.paymentService.RefundOrder(c.Request.Context(), appID, orderNo, &req)
	if err != nil {
		if err.Error() == "order not found" {
			c.JSON(http.StatusNotFound, NotFoundResponse("Order not found"))
		} else {
			c.JSON(http.StatusBadRequest, ErrorResponse(400, err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(refundOrder))
}

// HandleNotify 处理支付回调通知
// @Summary 处理支付回调通知
// @Description 接收并处理来自支付平台的异步通知
// @Tags 支付管理
// @Accept json
// @Produce json
// @Param channel path string true "支付渠道" Enums(alipay, wechat, qq)
// @Success 200 {string} string "success"
// @Failure 400 {object} Response
// @Router /api/v1/payments/notify/{channel} [post]
func (h *PaymentHandler) HandleNotify(c *gin.Context) {
	channel := c.Param("channel")
	if channel == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Payment channel is required",
			Data:    nil,
		})
		return
	}

	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		logger.WithError(err).Error("Failed to read request body")
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Failed to read request body",
			Data:    nil,
		})
		return
	}

	// 记录回调日志
	logger.WithFields(map[string]interface{}{
		"channel": channel,
		"body":    string(body),
		"headers": c.Request.Header,
	}).Info("Received payment notification")

	// 处理回调
	err = h.paymentService.HandleNotify(c.Request.Context(), channel, c.Request)
	if err != nil {
		logger.WithError(err).Error("Failed to handle payment notification")
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Failed to process notification",
			Data:    nil,
		})
		return
	}

	// 根据不同支付渠道返回相应格式
	switch channel {
	case "alipay":
		c.String(http.StatusOK, "success")
	case "wechat":
		c.XML(http.StatusOK, gin.H{
			"return_code": "SUCCESS",
			"return_msg":  "OK",
		})
	default:
		c.String(http.StatusOK, "success")
	}
}

// ListOrders 获取订单列表
// @Summary 获取订单列表
// @Description 获取商户的订单列表
// @Tags 支付管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param X-API-Key header string true "商户API密钥"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} Response{data=object}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Router /api/v1/payments/orders [get]
func (h *PaymentHandler) ListOrders(c *gin.Context) {
	// 获取商户ID
	_ = uint64(1) // TODO: 从API Key获取真实的商户ID

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	_ = (page - 1) * pageSize // offset

	// TODO: 调用service获取订单列表
	// orders, total, err := h.paymentService.ListOrders(c.Request.Context(), merchantID, offset, pageSize)

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data: gin.H{
			"list":      []interface{}{},
			"total":     0,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// sanitizeHeaders 清理敏感的请求头信息，用于日志记录
func (h *PaymentHandler) sanitizeHeaders(headers map[string][]string) map[string]interface{} {
	sanitized := make(map[string]interface{})

	// 需要记录的重要头信息
	importantHeaders := []string{
		"Content-Type",
		"Content-Length",
		"Wechatpay-Signature",
		"Wechatpay-Timestamp",
		"Wechatpay-Nonce",
		"Wechatpay-Serial",
		"Authorization",
		"Accept",
		"Accept-Encoding",
	}

	for _, header := range importantHeaders {
		if values, exists := headers[header]; exists {
			if len(values) > 0 {
				// 对敏感信息进行脱敏处理
				if header == "Wechatpay-Signature" || header == "Authorization" {
					if len(values[0]) > 10 {
						sanitized[header] = values[0][:10] + "..."
					} else {
						sanitized[header] = "***"
					}
				} else {
					sanitized[header] = values[0]
				}
			}
		}
	}

	return sanitized
}
