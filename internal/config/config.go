package config

import (
	"errors"
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Server    ServerConfig    `mapstructure:"server"`
	Database  DatabaseConfig  `mapstructure:"database"`
	Redis     RedisConfig     `mapstructure:"redis"`
	Payment   PaymentConfig   `mapstructure:"payment"`
	Robot     RobotConfig     `mapstructure:"robot"`
	Log       LogConfig       `mapstructure:"log"`
	Scheduler SchedulerConfig `mapstructure:"scheduler"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port    int    `mapstructure:"port"`
	Mode    string `mapstructure:"mode"`
	BaseURL string `mapstructure:"base_url"` // 服务器基础URL，用于构建回调地址
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	Charset         string `mapstructure:"charset"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr     string `mapstructure:"addr"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

// PaymentConfig 支付配置
type PaymentConfig struct {
	Wechat WechatConfig `mapstructure:"wechat"`
}

// RobotConfig 机器人服务配置
type RobotConfig struct {
	BaseURL   string `mapstructure:"base_url"`
	SharedKey string `mapstructure:"shared_key"`
	Timeout   int    `mapstructure:"timeout"`
}

// WechatConfig 微信支付V3 API配置
type WechatConfig struct {
	AppID  string `mapstructure:"app_id"`  // 微信应用ID
	MchID  string `mapstructure:"mch_id"`  // 商户号
	IsProd bool   `mapstructure:"is_prod"` // 是否生产环境

	// V3 API必需配置
	APIv3Key string `mapstructure:"apiv3_key"` // V3 API密钥（32位）
	SerialNo string `mapstructure:"serial_no"` // 商户证书序列号（40位十六进制）

	// 商户私钥配置（二选一）
	PrivateKey     string `mapstructure:"private_key"`      // 商户私钥内容（PEM格式）
	PrivateKeyPath string `mapstructure:"private_key_path"` // 商户私钥文件路径

	// 微信支付平台证书配置（用于验签，可选但推荐）
	PlatformCertPath     string `mapstructure:"platform_cert_path"`      // 微信支付平台证书文件路径
	PlatformCert         string `mapstructure:"platform_cert"`           // 微信支付平台证书内容（PEM格式）
	PlatformCertSerialNo string `mapstructure:"platform_cert_serial_no"` // 微信支付平台证书序列号
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// SchedulerConfig 调度器配置
type SchedulerConfig struct {
	CloseExpiredOrders CloseExpiredOrdersConfig `mapstructure:"close_expired_orders"`
	DailyReconcile     DailyReconcileConfig     `mapstructure:"daily_reconcile"`
}

// CloseExpiredOrdersConfig 关闭过期订单配置
type CloseExpiredOrdersConfig struct {
	Enabled        bool   `mapstructure:"enabled"`         // 是否启用
	Cron           string `mapstructure:"cron"`            // cron表达式
	TimeoutMinutes int    `mapstructure:"timeout_minutes"` // 超时时间（分钟）
	BatchSize      int    `mapstructure:"batch_size"`      // 批处理大小
	ScanDays       int    `mapstructure:"scan_days"`       // 扫描天数范围
	LockKey        string `mapstructure:"lock_key"`        // 分布式锁key
	LockTTL        int    `mapstructure:"lock_ttl"`        // 锁TTL（秒）
}

// DailyReconcileConfig 日对账配置
type DailyReconcileConfig struct {
	Enabled        bool     `mapstructure:"enabled"`         // 是否启用
	Cron           string   `mapstructure:"cron"`            // cron表达式
	TimeoutMinutes int      `mapstructure:"timeout_minutes"` // 超时时间（分钟）
	Channels       []string `mapstructure:"channels"`        // 对账渠道
	DelayDays      int      `mapstructure:"delay_days"`      // 延迟天数（对账前N天的数据）
	LockKey        string   `mapstructure:"lock_key"`        // 分布式锁key
	LockTTL        int      `mapstructure:"lock_ttl"`        // 锁TTL（秒）
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("/etc/pay-core")

	// 设置环境变量前缀
	viper.SetEnvPrefix("PAY_CORE")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.base_url", "http://localhost:8080") // 默认本地地址，生产环境需要修改

	// 数据库默认配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.username", "root")
	viper.SetDefault("database.password", "")
	viper.SetDefault("database.database", "pay_core")
	viper.SetDefault("database.charset", "utf8mb4")
	viper.SetDefault("database.max_idle_conns", 10)
	viper.SetDefault("database.max_open_conns", 100)
	viper.SetDefault("database.conn_max_lifetime", 3600)

	// Redis默认配置
	viper.SetDefault("redis.addr", "localhost:6379")
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)

	// 微信支付默认配置
	viper.SetDefault("payment.wechat.is_prod", false)

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output", "stdout")

	// 客户端认证默认配置
	viper.SetDefault("auth.app_secret", "pay-core-client-secret")
	viper.SetDefault("auth.timestamp_tolerance", 300) // 5分钟
	viper.SetDefault("auth.nonce_ttl", 600)           // 10分钟

	// 调度器默认配置
	viper.SetDefault("scheduler.close_expired_orders.enabled", true)
	viper.SetDefault("scheduler.close_expired_orders.cron", "0 */5 * * * *") // 每5分钟
	viper.SetDefault("scheduler.close_expired_orders.timeout_minutes", 5)
	viper.SetDefault("scheduler.close_expired_orders.batch_size", 100)
	viper.SetDefault("scheduler.close_expired_orders.scan_days", 7) // 只扫描最近7天
	viper.SetDefault("scheduler.close_expired_orders.lock_key", "pay_core:close_expired_orders")
	viper.SetDefault("scheduler.close_expired_orders.lock_ttl", 300) // 5分钟

	// 日对账默认配置
	viper.SetDefault("scheduler.daily_reconcile.enabled", true)
	viper.SetDefault("scheduler.daily_reconcile.cron", "0 0 2 * * *") // 每天凌晨2点
	viper.SetDefault("scheduler.daily_reconcile.timeout_minutes", 30)
	viper.SetDefault("scheduler.daily_reconcile.channels", []string{"wechat"})
	viper.SetDefault("scheduler.daily_reconcile.delay_days", 1) // 对账前一天的数据
	viper.SetDefault("scheduler.daily_reconcile.lock_key", "pay_core:daily_reconcile")
	viper.SetDefault("scheduler.daily_reconcile.lock_ttl", 1800) // 30分钟
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		c.Username,
		c.Password,
		c.Host,
		c.Port,
		c.Database,
		c.Charset,
	)
}

// Validate 验证配置的完整性和正确性
func (c *Config) Validate() error {
	// 验证数据库配置
	if c.Database.Host == "" {
		return errors.New("database host is required")
	}
	if c.Database.Username == "" {
		return errors.New("database username is required")
	}
	if c.Database.Database == "" {
		return errors.New("database name is required")
	}

	// 验证支付配置
	if err := c.validatePaymentConfig(); err != nil {
		return fmt.Errorf("payment config validation failed: %w", err)
	}

	return nil
}

// validatePaymentConfig 验证支付配置
func (c *Config) validatePaymentConfig() error {
	// 验证微信支付V3 API配置
	if c.Payment.Wechat.AppID != "" {
		if c.Payment.Wechat.MchID == "" {
			return errors.New("wechat mch_id is required when app_id is set")
		}

		// V3 API必需配置验证
		if c.Payment.Wechat.APIv3Key == "" {
			return errors.New("wechat apiv3_key is required")
		}
		if len(c.Payment.Wechat.APIv3Key) != 32 {
			return errors.New("wechat apiv3_key must be 32 characters long")
		}
		if c.Payment.Wechat.SerialNo == "" {
			return errors.New("wechat serial_no is required")
		}
		if len(c.Payment.Wechat.SerialNo) != 40 {
			return errors.New("wechat serial_no must be 40 characters long (hex format)")
		}

		// 验证私钥配置（文件路径和内容二选一）
		hasPrivateKeyPath := c.Payment.Wechat.PrivateKeyPath != ""
		hasPrivateKeyContent := c.Payment.Wechat.PrivateKey != ""

		if !hasPrivateKeyPath && !hasPrivateKeyContent {
			return errors.New("wechat private key is required (either private_key_path or private_key)")
		}

		if hasPrivateKeyPath && hasPrivateKeyContent {
			return errors.New("wechat private key: cannot specify both private_key_path and private_key")
		}

		// 验证私钥内容格式
		if c.Payment.Wechat.PrivateKey != "" {
			if !strings.Contains(c.Payment.Wechat.PrivateKey, "BEGIN PRIVATE KEY") &&
				!strings.Contains(c.Payment.Wechat.PrivateKey, "BEGIN RSA PRIVATE KEY") {
				return errors.New("wechat private_key format is invalid")
			}
		}

		// 验证平台证书配置（可选但推荐）
		hasPlatformCertPath := c.Payment.Wechat.PlatformCertPath != ""
		hasPlatformCertContent := c.Payment.Wechat.PlatformCert != ""

		if hasPlatformCertPath && hasPlatformCertContent {
			return errors.New("wechat platform cert: cannot specify both platform_cert_path and platform_cert")
		}

		// 验证平台证书内容格式
		if c.Payment.Wechat.PlatformCert != "" {
			if !strings.Contains(c.Payment.Wechat.PlatformCert, "BEGIN CERTIFICATE") {
				return errors.New("wechat platform_cert format is invalid")
			}
		}
	}

	return nil
}
