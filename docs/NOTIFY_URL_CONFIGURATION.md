# Notify回调地址配置说明

## 问题描述

如果您没有收到支付平台的回调通知，很可能是因为notify回调地址配置不正确。

## 当前传递给支付平台的回调地址

系统会自动构建以下回调地址传递给支付平台：

### 付款通知地址
- **微信支付**: `{base_url}/notify/wechat`
- **支付宝**: `{base_url}/notify/alipay`

### 退款通知地址  
- **微信退款**: `{base_url}/notify/wechat/refund`
- **支付宝退款**: `{base_url}/notify/alipay/refund`

其中 `{base_url}` 来自配置文件中的 `server.base_url` 配置项。

## 配置方法

### 1. 修改配置文件

编辑 `configs/config.yaml` 文件：

```yaml
server:
  port: 8080
  mode: release
  base_url: "https://your-domain.com"  # 替换为您的实际域名
```

### 2. 配置要求

**base_url 必须满足以下条件：**

1. **外网可访问**: 支付平台必须能从互联网访问到这个地址
2. **使用HTTPS**: 生产环境建议使用HTTPS协议
3. **域名解析正确**: 确保域名能正确解析到您的服务器
4. **端口开放**: 确保防火墙允许外网访问对应端口

### 3. 配置示例

#### 开发环境（使用内网穿透）
```yaml
server:
  base_url: "https://abc123.ngrok.io"  # ngrok生成的临时域名
```

#### 测试环境
```yaml
server:
  base_url: "https://test-pay.your-company.com"
```

#### 生产环境
```yaml
server:
  base_url: "https://pay.your-company.com"
```

## 验证配置

### 1. 检查回调地址是否可访问

使用curl测试回调地址是否可以从外网访问：

```bash
# 测试付款回调地址
curl -X POST https://your-domain.com/notify/wechat

# 测试退款回调地址  
curl -X POST https://your-domain.com/notify/wechat/refund
```

### 2. 查看日志

启动服务后，查看日志中的回调地址：

```bash
# 查看服务日志
tail -f /var/log/pay-core/app.log | grep "notify"
```

### 3. 支付平台配置

确保在支付平台后台配置了正确的回调地址：

#### 微信支付商户平台
1. 登录微信支付商户平台
2. 进入"产品中心" -> "开发配置"
3. 设置"支付回调URL"为: `https://your-domain.com/notify/wechat`

#### 支付宝开放平台
1. 登录支付宝开放平台
2. 进入应用详情页
3. 设置"异步通知地址"为: `https://your-domain.com/notify/alipay`

## 常见问题

### 1. 仍然收不到回调通知

**可能原因：**
- 域名无法从外网访问
- 防火墙阻止了外网访问
- HTTPS证书问题
- 支付平台后台配置错误

**排查步骤：**
1. 使用在线工具测试域名是否可访问
2. 检查服务器防火墙设置
3. 验证HTTPS证书是否有效
4. 确认支付平台后台配置

### 2. 本地开发环境测试

**推荐使用内网穿透工具：**

#### 使用ngrok
```bash
# 安装ngrok
npm install -g ngrok

# 启动内网穿透
ngrok http 8080

# 复制生成的https地址到配置文件
```

#### 使用frp
```bash
# 配置frp客户端
./frpc -c frpc.ini
```

### 3. 配置更新后不生效

**解决方法：**
1. 重启服务
2. 清除支付平台缓存
3. 重新创建支付订单测试

## 监控建议

### 1. 设置回调监控

监控回调接收情况：

```sql
-- 查看最近的回调日志
SELECT * FROM notify_logs 
WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY created_at DESC;

-- 统计回调成功率
SELECT 
    notify_type,
    COUNT(*) as total,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success,
    ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM notify_logs 
WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY notify_type;
```

### 2. 设置告警

当回调失败率过高时及时告警：
- 回调成功率低于95%
- 连续10分钟没有收到回调
- 回调响应时间过长

## 总结

正确配置 `server.base_url` 是接收支付回调通知的关键。确保：

1. ✅ 配置了正确的外网可访问域名
2. ✅ 使用HTTPS协议（生产环境）
3. ✅ 防火墙允许外网访问
4. ✅ 支付平台后台配置正确
5. ✅ 定期监控回调接收情况

配置正确后，系统将能够正常接收支付平台的回调通知，确保订单状态及时更新。
