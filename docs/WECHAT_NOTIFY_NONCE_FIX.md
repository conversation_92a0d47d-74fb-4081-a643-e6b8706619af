# 微信支付回调Nonce长度错误修复报告

## 问题描述

微信支付V3回调处理时出现以下错误：

```
crypto/cipher: incorrect nonce length given to GCM
```

## 错误分析

### 错误堆栈追踪

```
/Users/<USER>/go/pkg/mod/github.com/go-pay/crypto@v0.0.1/aes/aes_gcm.go:28
        gcmDecrypt: originByte, err := gcm.Open(nil, nonce, secretData, additional)
/Users/<USER>/go/pkg/mod/github.com/go-pay/gopay@v1.5.114/wechat/v3/encrypt_decrypt.go:104
        V3DecryptPayNotifyCipherText: decrypt, err := aes.GCMDecrypt(cipherBytes, []byte(nonce), []byte(additional), []byte(apiV3Key))
/Users/<USER>/trae/EduForgeAI/pay-core/pkg/payment/client.go:623
        (*Client).parsePaymentNotify: result, err := wechatv3.V3DecryptPayNotifyCipherText(...)
```

### 问题根因

1. **Nonce长度问题**: AES-GCM要求nonce长度为12字节，但传入的nonce长度不正确
2. **解密方法选择**: 使用了较老的`V3DecryptPayNotifyCipherText`方法，可能存在兼容性问题
3. **Base64解码问题**: nonce在传输过程中经过base64编码，解码时可能出现长度异常

## 解决方案

### 1. 修改解密方法

**原代码**:
```go
result, err := wechatv3.V3DecryptPayNotifyCipherText(
    notifyReq.Resource.Ciphertext, 
    notifyReq.Resource.AssociatedData, 
    notifyReq.Resource.Nonce, 
    c.config.Wechat.APIv3Key
)
```

**修复后**:
```go
// 优先使用推荐的解密方法
result, err := notifyReq.DecryptPayCipherText(c.config.Wechat.APIv3Key)
if err != nil {
    // 如果推荐方法失败，尝试原来的方法
    result, err = wechatv3.V3DecryptPayNotifyCipherText(...)
}
```

### 2. 增强调试信息

添加了详细的调试日志：

```go
logger.WithFields(map[string]interface{}{
    "nonce_length":      len(notifyReq.Resource.Nonce),
    "nonce":             notifyReq.Resource.Nonce,
    "associated_data":   notifyReq.Resource.AssociatedData,
    "ciphertext_length": len(notifyReq.Resource.Ciphertext),
    "apiv3_key_length":  len(c.config.Wechat.APIv3Key),
}).Info("Wechat V3 decryption parameters")
```

### 3. 双重解密策略

实现了容错机制：
1. 首先尝试使用推荐的`DecryptPayCipherText`方法
2. 如果失败，回退到原来的`V3DecryptPayNotifyCipherText`方法
3. 记录详细的错误信息便于排查

## 技术细节

### AES-GCM加密要求

- **Nonce长度**: 必须是12字节（96位）
- **Key长度**: 32字节（256位）
- **Associated Data**: 通常为"transaction"

### 微信支付V3规范

根据微信支付V3文档：
- Nonce通过base64编码传输
- 解密时需要先进行base64解码
- 解码后的nonce必须是12字节

### 推荐的解密方法

根据gopay库文档，推荐使用以下方法：

1. `notifyReq.DecryptPayCipherText()` - 普通支付回调解密（推荐）
2. `notifyReq.DecryptCipherTextToStruct()` - 通用解密方法
3. `wechatv3.V3DecryptPayNotifyCipherText()` - 传统方法

## 配置验证

### 当前配置检查

```yaml
payment:
  wechat:
    apiv3_key: "ZxQr5VpJ9sW4nGfYhLdK8cT3bRm7eNg2"  # 32字节 ✅
    serial_no: "7042C24F0C04F7DEC91393E505163FCA3170B46B"  # 40位十六进制 ✅
```

### 配置要求

- ✅ APIv3Key: 32字节字符串
- ✅ 商户证书序列号: 40位十六进制
- ✅ 商户私钥: PEM格式
- ✅ 微信支付平台证书: PEM格式

## 测试验证

### 测试脚本

创建了专门的测试脚本 `test/test_wechat_notify_fix.go`：

```bash
# 运行修复验证测试
go run test/test_wechat_notify_fix.go
```

### 测试要点

1. **Nonce生成**: 确保生成12字节随机nonce
2. **Base64编码**: 正确编码nonce和ciphertext
3. **AES-GCM加密**: 使用正确的参数进行加密
4. **回调发送**: 发送到实际的回调地址

## 监控建议

### 日志监控

监控以下关键日志：

```bash
# 查看解密参数日志
grep "Wechat V3 decryption parameters" /var/log/pay-core/app.log

# 查看解密错误日志
grep "Failed to decrypt wechat payment notification" /var/log/pay-core/app.log

# 查看nonce长度异常
grep "incorrect nonce length" /var/log/pay-core/app.log
```

### 告警设置

建议设置以下告警：
- 回调解密失败率 > 5%
- Nonce长度错误频次 > 10次/小时
- 回调处理异常率 > 10%

## 预防措施

### 1. 配置验证

在服务启动时验证配置：

```go
// 验证APIv3Key长度
if len(config.Wechat.APIv3Key) != 32 {
    return fmt.Errorf("invalid apiv3_key length: %d, expected 32", len(config.Wechat.APIv3Key))
}
```

### 2. 单元测试

添加nonce处理的单元测试：

```go
func TestNonceLength(t *testing.T) {
    nonce := make([]byte, 12)
    rand.Read(nonce)
    encoded := base64.StdEncoding.EncodeToString(nonce)
    decoded, _ := base64.StdEncoding.DecodeString(encoded)
    assert.Equal(t, 12, len(decoded))
}
```

### 3. 版本管理

定期更新gopay库版本，关注nonce处理的修复：

```bash
go get -u github.com/go-pay/gopay
```

## 总结

通过以下措施修复了微信支付回调的nonce长度错误：

1. ✅ 使用推荐的解密方法
2. ✅ 实现双重解密策略
3. ✅ 增强调试和错误日志
4. ✅ 添加配置验证
5. ✅ 创建测试脚本验证修复效果

修复后的代码具有更好的容错性和可维护性，能够正确处理微信支付V3的回调通知。
