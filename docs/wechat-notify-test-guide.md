# 微信支付回调测试指南

## 概述

本文档详细说明了微信支付V3 API回调的实现原理和测试方法，包括支付成功回调和退款成功回调的测试。

## 微信支付V3回调机制

### 1. 回调数据结构

微信支付V3使用加密的回调数据格式：

```json
{
  "id": "notify_1234567890",
  "create_time": "2024-01-01T10:00:00+08:00",
  "event_type": "TRANSACTION.SUCCESS",
  "resource_type": "encrypt-resource",
  "resource": {
    "algorithm": "AEAD_AES_256_GCM",
    "ciphertext": "base64_encoded_encrypted_data",
    "associated_data": "transaction",
    "nonce": "base64_encoded_nonce"
  },
  "summary": "支付成功"
}
```

### 2. 加密算法

- **算法**: AES-256-GCM
- **密钥**: APIv3Key (32位字符串)
- **Nonce**: 12字节随机数
- **Associated Data**: "transaction"

### 3. 签名验证

微信支付V3使用RSA-PSS签名算法：

```
签名字符串 = HTTP方法\n请求路径\n时间戳\n随机数\n请求体\n
```

## 项目中的回调实现

### 1. 回调接口路由

```go
// cmd/server/main.go
notify := r.Group("/notify")
{
    notify.POST("/:channel", paymentHandler.Notify)
    notify.POST("/:channel/refund", paymentHandler.RefundNotify)
}
```

### 2. 回调处理流程

1. **接收回调**: `PaymentHandler.Notify()` 接收POST请求
2. **解析数据**: `PaymentService.HandleNotify()` 解析回调数据
3. **验证签名**: 使用微信支付平台证书验证签名
4. **解密数据**: 使用APIv3Key解密resource字段
5. **处理业务**: 根据事件类型处理支付或退款逻辑
6. **记录日志**: 保存回调日志到数据库

### 3. 关键代码文件

- `internal/handler/payment_handler.go`: 回调接口处理
- `internal/service/payment_service.go`: 回调业务逻辑
- `pkg/payment/client.go`: 微信支付客户端和数据解析
- `internal/model/notify_log.go`: 回调日志模型

## 测试方法

### 方法1: 使用Go测试脚本

我们提供了完整的Go测试脚本，能够构造正确的加密数据和签名：

```bash
# 运行完整的回调测试
go run test/simple_wechat_notify_test.go
```

**特点**:
- ✅ 正确的AES-256-GCM加密
- ✅ 正确的RSA-PSS签名
- ✅ 符合微信支付V3规范
- ✅ 测试支付和退款两种场景

### 方法2: 使用curl简化测试

提供了简化的curl测试脚本，用于快速验证接口可用性：

```bash
# 运行简化的curl测试
./scripts/test_notify_curl.sh
```

**特点**:
- ⚠️ 简化的测试数据（不包含真实加密）
- ✅ 快速验证接口响应
- ✅ 测试基本的路由和参数处理

### 方法3: 使用自动化测试脚本

```bash
# 运行自动化测试
./scripts/test_wechat_notify.sh
```

## 配置要求

### 1. 微信支付配置

确保 `configs/config.yaml` 中包含正确的配置：

```yaml
payment:
  wechat:
    app_id: "wxcf9a42f63c23a637"
    mch_id: "**********"
    is_prod: false
    apiv3_key: "ZxQr5VpJ9sW4nGfYhLdK8cT3bRm7eNg2"
    serial_no: "7042C24F0C04F7DEC91393E505163FCA3170B46B"
    private_key_path: "configs/**********_20250822_cert/apiclient_key.pem"
```

### 2. 证书文件

确保以下证书文件存在：
- `configs/**********_20250822_cert/apiclient_key.pem`: 商户私钥
- `configs/pub_key.pem`: 微信支付平台证书（可选）

## 测试场景

### 1. 支付成功回调

**事件类型**: `TRANSACTION.SUCCESS`

**解密后的数据结构**:
```json
{
  "appid": "wxcf9a42f63c23a637",
  "mchid": "**********",
  "out_trade_no": "TEST_NOTIFY_1234567890",
  "transaction_id": "wx_1234567890",
  "trade_type": "NATIVE",
  "trade_state": "SUCCESS",
  "trade_state_desc": "支付成功",
  "bank_type": "CMC",
  "success_time": "2024-01-01T10:00:00+08:00",
  "payer": {
    "openid": "test_openid_123"
  },
  "amount": {
    "total": 10000,
    "payer_total": 10000,
    "currency": "CNY",
    "payer_currency": "CNY"
  }
}
```

### 2. 退款成功回调

**事件类型**: `REFUND.SUCCESS`

**解密后的数据结构**:
```json
{
  "mchid": "**********",
  "out_trade_no": "TEST_NOTIFY_1234567890",
  "transaction_id": "wx_1234567890",
  "out_refund_no": "REFUND_1234567890",
  "refund_id": "wx_refund_1234567890",
  "refund_status": "SUCCESS",
  "success_time": "2024-01-01T10:00:00+08:00",
  "recv_account": "招商银行信用卡0403",
  "user_received_account": "招商银行信用卡0403",
  "amount": {
    "total": 10000,
    "refund": 5000,
    "payer_total": 10000,
    "payer_refund": 5000,
    "currency": "CNY"
  }
}
```

## 预期结果

### 成功响应

- **HTTP状态码**: 200
- **响应内容**: "SUCCESS"

### 失败响应

- **HTTP状态码**: 400
- **响应内容**: "FAIL"

## 日志记录

所有回调都会记录到 `notify_logs` 表：

```sql
SELECT * FROM notify_logs 
WHERE notify_type = 'PAYMENT' 
ORDER BY created_at DESC 
LIMIT 10;
```

## 故障排查

### 1. 签名验证失败

- 检查私钥文件是否存在
- 确认签名字符串格式正确
- 验证时间戳是否在有效范围内

### 2. 解密失败

- 确认APIv3Key配置正确
- 检查nonce和associated_data
- 验证加密算法是否为AES-256-GCM

### 3. 订单不存在

- 确认测试订单已创建
- 检查订单号格式
- 验证应用ID权限

## 参考文档

- [微信支付V3 API文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)
- [支付结果通知](https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_5.shtml)
- [退款结果通知](https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_11.shtml)
