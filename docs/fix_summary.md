# 微信支付查询订单状态失败问题修复总结

## 问题现象

延时队列触发的微信支付订单状态查询失败，错误日志：

```json
{
  "channel": "wechat",
  "duration": 268,
  "error": "wechat V3 query order failed: ",
  "level": "error",
  "msg": "Failed to query payment order status",
  "order_no": "20250823225357925ba5",
  "time": "2025-08-23 22:56:40",
  "transaction_id": ""
}
```

## 问题分析过程

### 1. 初步怀疑
- 网络连接问题
- 证书配置问题
- 客户端初始化问题

### 2. 关键发现
通过对比发现：
- **QueryOrder API接口**：可以正常调用微信支付查询
- **延时队列触发的查询**：失败

这说明证书、网络、客户端初始化都没有问题，问题出在代码逻辑上。

### 3. 深入调试
添加详细日志后发现：

```
response_code=0 
response_error= 
response_body="&{...TradeState:NOTPAY...}"
```

关键信息：
- `response_code=0`：不是HTTP 200
- `response_error=`：错误信息为空
- 响应体包含有效的订单信息

## 根本原因

**微信支付V3 API的响应状态码规则与HTTP标准不同**：

- **成功**：`Code = 0`（不是200）
- **失败**：`Code != 0`

原代码错误地使用了 `wxRsp.Code != 200` 来判断失败：

```go
// 错误的判断逻辑
if wxRsp.Code != 200 {
    return nil, fmt.Errorf("wechat V3 query order failed: %s", wxRsp.Error)
}
```

这导致所有成功的查询（Code=0）都被误判为失败。

## 修复方案

### 修正前的代码
```go
if wxRsp.Code != 200 {
    return nil, fmt.Errorf("wechat V3 query order failed: %s", wxRsp.Error)
}
```

### 修正后的代码
```go
// 检查响应状态
// 注意：微信支付V3 API成功时返回Code=0，失败时返回非0值
if wxRsp.Code != 0 {
    // 添加更详细的响应日志
    logger.WithFields(map[string]interface{}{
        "order_no":       req.OrderNo,
        "transaction_id": req.TransactionID,
        "response_code":  wxRsp.Code,
        "response_error": wxRsp.Error,
        "response_body":  fmt.Sprintf("%+v", wxRsp.Response),
    }).Error("Wechat V3 query order response error")
    
    errorMsg := wxRsp.Error
    if errorMsg == "" {
        errorMsg = fmt.Sprintf("wechat API returned error code: %d", wxRsp.Code)
    }
    return nil, fmt.Errorf("wechat V3 query order failed: %s", errorMsg)
}

// 检查响应内容是否有效
if wxRsp.Response == nil {
    logger.WithFields(map[string]interface{}{
        "order_no":       req.OrderNo,
        "transaction_id": req.TransactionID,
        "response_code":  wxRsp.Code,
    }).Error("Wechat V3 query order response is nil")
    return nil, fmt.Errorf("wechat V3 query order failed: empty response")
}
```

## 修复验证

修复后的测试结果：

```
INFO[0000] Successfully queried payment order status     
amount=100 
channel=wechat 
duration=337 
order_no=20250823225357925ba5 
status=NOTPAY 
transaction_id=
```

查询成功，返回了正确的订单状态信息。

## 经验教训

1. **API文档的重要性**：不同支付平台的API响应格式可能不同，需要仔细阅读文档
2. **详细日志的价值**：通过添加详细的调试日志，快速定位了问题
3. **对比测试的有效性**：通过对比正常工作的接口和失败的接口，缩小了问题范围
4. **不要假设标准**：不能假设所有API都遵循HTTP标准状态码

## 影响范围

此修复解决了以下问题：
- 延时队列触发的订单状态轮询失败
- 订单状态无法及时更新
- 可能导致的订单状态不一致问题

## 后续建议

1. **添加单元测试**：为微信支付查询功能添加单元测试
2. **监控告警**：添加对支付查询失败的监控告警
3. **文档更新**：更新开发文档，说明微信V3 API的特殊性
4. **代码审查**：在类似的第三方API集成中，注意响应格式的差异
