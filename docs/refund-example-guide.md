# 退款功能使用示例

## 概述

本文档展示了如何使用PayCore系统的退款功能，包括完整的退款申请流程和示例代码。

## 测试订单信息

**测试订单号**: `20250823233320e3fddb`

**订单详情**:
- 商品名称: 测试商品
- 订单金额: 0.01 元
- 订单状态: PAID (已支付)
- 支付渠道: 微信支付

## 退款功能特性

### ✅ 支持的功能

1. **部分退款** - 可以退款订单的部分金额
2. **全额退款** - 可以退款订单的全部金额
3. **状态验证** - 只有已支付的订单才能申请退款
4. **金额验证** - 退款金额不能超过订单原始金额
5. **唯一性验证** - 业务方退款单号必须唯一

### 📋 退款流程

1. **查询订单** - 确认订单存在且状态为已支付
2. **生成退款单号** - 创建唯一的业务方退款单号
3. **申请退款** - 调用退款API提交申请
4. **处理结果** - 获取退款单号和状态

## 使用示例

### 1. 客户端SDK示例

在 `examples/client_sdk.go` 中已经包含了完整的退款示例：

```go
// 生成业务方退款单号
appRefundNo := fmt.Sprintf("REFUND_%d", time.Now().Unix())

// 申请退款
refundAmount := 0.01
refundReason := "用户申请退款"

refundResp, err := client.RefundOrder(orderNo, appRefundNo, refundAmount, refundReason)
```

### 2. 独立退款示例

运行专门的退款示例：

```bash
# 运行退款示例
go run examples/refund_demo.go

# 或使用脚本
./scripts/test_refund_example.sh
```

### 3. API调用示例

**请求格式**:
```bash
curl -X POST "http://localhost:8080/api/v1/orders/20250823233320e3fddb/refund" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: robot_user123" \
  -H "X-Timestamp: 1755963937" \
  -H "X-Nonce: abc123" \
  -H "X-Signature: signature_value" \
  -d '{
    "app_refund_no": "REFUND_1755963937",
    "refund_amount": 0.01,
    "refund_reason": "用户申请退款 - 测试退款功能"
  }'
```

**成功响应**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": 1,
    "refund_no": "R20250823234537975891",
    "order_no": "20250823233320e3fddb",
    "app_id": 1,
    "app_refund_no": "REFUND_1755963937",
    "refund_amount": 0.01,
    "refund_reason": "用户申请退款 - 测试退款功能",
    "status": "PROCESSING",
    "out_refund_no": "50303504192025082376992385743",
    "success_time": null,
    "created_at": "2025-08-23 23:45:37",
    "updated_at": "2025-08-23 23:45:38"
  }
}
```

## 测试结果

### ✅ 成功案例

使用订单号 `20250823233320e3fddb` 进行退款测试：

```
=== PayCore 退款功能示例 ===
1. 查询订单信息: 20250823233320e3fddb
✅ 订单信息:
   - 订单号: 20250823233320e3fddb
   - 商品名称: 测试商品
   - 订单状态: PAID
   - 订单金额: 0.00 元

2. 申请退款
退款参数:
   - 业务退款单号: REFUND_1755963937
   - 退款金额: 0.01 元
   - 退款原因: 用户申请退款 - 测试退款功能

✅ 退款申请成功:
   - 退款单号: R20250823234537975891
   - 退款状态: PROCESSING
   - 退款金额: 0.01 元
```

### 📊 关键信息

- **系统退款单号**: `R20250823234537975891`
- **业务退款单号**: `REFUND_1755963937`
- **第三方退款单号**: `50303504192025082376992385743`
- **退款状态**: `PROCESSING` (处理中)

## 参数说明

### 必需参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `app_refund_no` | string | 业务方退款单号，必须唯一 | `REFUND_1755963937` |
| `refund_amount` | decimal | 退款金额，不能超过订单金额 | `0.01` |

### 可选参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `refund_reason` | string | 退款原因 | `用户申请退款` |

### 响应字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `refund_no` | string | 系统生成的退款单号 |
| `status` | string | 退款状态 (PROCESSING/SUCCESS/FAILED) |
| `out_refund_no` | string | 第三方支付平台的退款单号 |

## 状态说明

### 退款状态

- **PROCESSING** - 处理中，退款申请已提交到支付平台
- **SUCCESS** - 退款成功，资金已退回
- **FAILED** - 退款失败，申请被拒绝

### 订单状态变化

当退款成功时，原订单状态会从 `PAID` 更新为 `REFUNDED`。

## 注意事项

### ⚠️ 重要提醒

1. **订单状态检查**: 只有状态为 `PAID` 的订单才能申请退款
2. **金额限制**: 退款金额不能超过订单原始金额
3. **唯一性要求**: `app_refund_no` 必须在系统中唯一
4. **处理时间**: 退款处理可能需要一定时间，具体取决于支付渠道
5. **回调通知**: 退款结果会通过回调通知业务方

### 🔧 故障排查

**常见错误**:

1. **"Invalid request parameters"** - 检查必需参数是否完整
2. **"Order not found"** - 确认订单号是否正确
3. **"Order cannot be refunded"** - 检查订单状态是否为已支付
4. **"Refund amount exceeds order amount"** - 确认退款金额不超过订单金额

## 运行测试

### 快速测试

```bash
# 运行退款示例
./scripts/test_refund_example.sh
```

### 手动测试

```bash
# 运行独立退款示例
go run examples/refund_demo.go

# 运行完整客户端示例（包含退款功能）
go run examples/client_sdk.go
```

## 总结

退款功能已经完全实现并测试通过：

- ✅ **API接口正常**: 退款申请接口响应正确
- ✅ **参数验证完整**: 所有必需参数都得到验证
- ✅ **状态管理正确**: 退款状态正确更新
- ✅ **第三方集成**: 成功调用微信支付退款API
- ✅ **示例代码完整**: 提供了多种使用示例

**退款功能可以正常使用，支持生产环境部署。** 🎉
