package main

import (
	"fmt"
	"time"

	"pay-core/pkg/lock"
	"pay-core/pkg/queue"
)

func main() {
	fmt.Println("=== 代码逻辑验证测试 ===")

	// 测试1: 验证锁键生成
	testLockKeyGeneration()

	// 测试2: 验证队列任务创建
	testQueueTaskCreation()

	// 测试3: 验证任务类型常量
	testTaskTypeConstants()

	fmt.Println("\n=== 所有逻辑测试完成 ===")
}

func testLockKeyGeneration() {
	fmt.Println("\n--- 测试锁键生成 ---")

	// 测试支付锁键
	orderNo := "TEST_ORDER_001"
	paymentLockKey := lock.PaymentLockKey(orderNo)
	expectedPaymentKey := "payment_lock:TEST_ORDER_001"
	
	if paymentLockKey == expectedPaymentKey {
		fmt.Printf("✅ 支付锁键生成正确: %s\n", paymentLockKey)
	} else {
		fmt.Printf("❌ 支付锁键生成错误: 期望 %s, 实际 %s\n", expectedPaymentKey, paymentLockKey)
	}

	// 测试退款锁键
	refundNo := "TEST_REFUND_001"
	refundLockKey := lock.RefundLockKey(refundNo)
	expectedRefundKey := "refund_lock:TEST_REFUND_001"
	
	if refundLockKey == expectedRefundKey {
		fmt.Printf("✅ 退款锁键生成正确: %s\n", refundLockKey)
	} else {
		fmt.Printf("❌ 退款锁键生成错误: 期望 %s, 实际 %s\n", expectedRefundKey, refundLockKey)
	}
}

func testQueueTaskCreation() {
	fmt.Println("\n--- 测试队列任务创建 ---")

	orderNo := "TEST_ORDER_001"
	refundNo := "TEST_REFUND_001"
	testTime := time.Now().Add(5 * time.Minute)

	// 测试订单过期任务创建
	orderExpireTask := queue.CreateOrderExpireTask(orderNo, testTime)
	if orderExpireTask.ID == "expire_TEST_ORDER_001" &&
		orderExpireTask.Type == queue.TaskTypeOrderExpire &&
		orderExpireTask.Data["order_no"] == orderNo {
		fmt.Printf("✅ 订单过期任务创建正确: ID=%s, Type=%s\n", orderExpireTask.ID, orderExpireTask.Type)
	} else {
		fmt.Printf("❌ 订单过期任务创建错误\n")
	}

	// 测试订单轮询任务创建
	orderPollTask := queue.CreateOrderPollTask(orderNo, testTime, 1)
	if orderPollTask.ID == "poll_TEST_ORDER_001_1" &&
		orderPollTask.Type == queue.TaskTypeOrderPoll &&
		orderPollTask.Data["order_no"] == orderNo &&
		orderPollTask.Data["poll_count"] == 1 {
		fmt.Printf("✅ 订单轮询任务创建正确: ID=%s, Type=%s\n", orderPollTask.ID, orderPollTask.Type)
	} else {
		fmt.Printf("❌ 订单轮询任务创建错误\n")
	}

	// 测试退款过期任务创建
	refundExpireTask := queue.CreateRefundExpireTask(refundNo, testTime)
	if refundExpireTask.ID == "refund_expire_TEST_REFUND_001" &&
		refundExpireTask.Type == queue.TaskTypeRefundExpire &&
		refundExpireTask.Data["refund_no"] == refundNo {
		fmt.Printf("✅ 退款过期任务创建正确: ID=%s, Type=%s\n", refundExpireTask.ID, refundExpireTask.Type)
	} else {
		fmt.Printf("❌ 退款过期任务创建错误\n")
	}

	// 测试退款轮询任务创建
	refundPollTask := queue.CreateRefundPollTask(refundNo, testTime, 2)
	if refundPollTask.ID == "refund_poll_TEST_REFUND_001_2" &&
		refundPollTask.Type == queue.TaskTypeRefundPoll &&
		refundPollTask.Data["refund_no"] == refundNo &&
		refundPollTask.Data["poll_count"] == 2 {
		fmt.Printf("✅ 退款轮询任务创建正确: ID=%s, Type=%s\n", refundPollTask.ID, refundPollTask.Type)
	} else {
		fmt.Printf("❌ 退款轮询任务创建错误\n")
	}
}

func testTaskTypeConstants() {
	fmt.Println("\n--- 测试任务类型常量 ---")

	// 验证任务类型常量
	expectedTypes := map[string]string{
		"order_expire":  queue.TaskTypeOrderExpire,
		"order_poll":    queue.TaskTypeOrderPoll,
		"refund_expire": queue.TaskTypeRefundExpire,
		"refund_poll":   queue.TaskTypeRefundPoll,
	}

	allCorrect := true
	for expected, actual := range expectedTypes {
		if expected == actual {
			fmt.Printf("✅ 任务类型常量正确: %s\n", actual)
		} else {
			fmt.Printf("❌ 任务类型常量错误: 期望 %s, 实际 %s\n", expected, actual)
			allCorrect = false
		}
	}

	if allCorrect {
		fmt.Printf("✅ 所有任务类型常量验证通过\n")
	}
}
