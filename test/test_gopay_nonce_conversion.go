package main

import (
	"encoding/base64"
	"fmt"
)

// 测试 gopay 库中 []byte(nonce) 转换的行为

func main() {
	fmt.Println("=== 测试 gopay 库 nonce 转换 ===")

	// 模拟我们处理后的 nonce
	nonce := "ybWU0ak09bcX"
	decoded, _ := base64.StdEncoding.DecodeString(nonce)
	
	// 模拟我们的填充逻辑
	padded := make([]byte, 12)
	copy(padded, decoded)
	processedNonce := string(padded)
	
	fmt.Printf("原始 nonce: %s\n", nonce)
	fmt.Printf("解码后: %x (长度: %d)\n", decoded, len(decoded))
	fmt.Printf("填充后字节数组: %x (长度: %d)\n", padded, len(padded))
	fmt.Printf("转换为字符串: %q (长度: %d)\n", processedNonce, len(processedNonce))
	
	// 模拟 gopay 库中的转换：[]byte(nonce)
	fmt.Printf("\n--- 模拟 gopay 库转换 ---\n")
	gopayBytes := []byte(processedNonce)
	fmt.Printf("gopay 转换结果: %x (长度: %d)\n", gopayBytes, len(gopayBytes))
	
	// 验证长度
	if len(gopayBytes) == 12 {
		fmt.Println("✅ gopay 转换后长度正确 (12字节)")
	} else {
		fmt.Printf("❌ gopay 转换后长度错误: %d字节，期望12字节\n", len(gopayBytes))
	}
	
	// 检查是否有隐藏的问题
	fmt.Printf("\n--- 详细分析 ---\n")
	fmt.Printf("字符串中的每个字节:\n")
	for i, b := range []byte(processedNonce) {
		fmt.Printf("  [%d]: 0x%02x (%d)\n", i, b, b)
	}
	
	// 测试是否有不可见字符问题
	fmt.Printf("\n--- 测试不可见字符 ---\n")
	hasNullBytes := false
	for i, b := range []byte(processedNonce) {
		if b == 0 {
			fmt.Printf("发现 null 字节在位置 %d\n", i)
			hasNullBytes = true
		}
	}
	
	if hasNullBytes {
		fmt.Println("⚠️ 字符串包含 null 字节，这可能导致某些 C 库函数出现问题")
		
		// 尝试替代方案：重复填充
		fmt.Printf("\n--- 尝试重复填充策略 ---\n")
		repeated := make([]byte, 12)
		for i := 0; i < 12; i++ {
			repeated[i] = decoded[i%len(decoded)]
		}
		repeatedStr := string(repeated)
		
		fmt.Printf("重复填充结果: %x (长度: %d)\n", repeated, len(repeated))
		fmt.Printf("转换为字符串: %q (长度: %d)\n", repeatedStr, len(repeatedStr))
		
		gopayBytes2 := []byte(repeatedStr)
		fmt.Printf("gopay 转换结果: %x (长度: %d)\n", gopayBytes2, len(gopayBytes2))
		
		if len(gopayBytes2) == 12 {
			fmt.Println("✅ 重复填充策略可能更好")
		}
	} else {
		fmt.Println("✅ 字符串不包含 null 字节")
	}
}
