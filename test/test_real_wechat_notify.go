package main

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 使用真实的微信支付回调数据进行测试

func main() {
	fmt.Println("=== 使用真实微信支付回调数据测试 ===")

	// 从日志中提取的真实回调数据
	realNotifyData := `{
		"id": "notify_real_test",
		"create_time": "2025-08-24T10:42:40+08:00",
		"event_type": "TRANSACTION.SUCCESS",
		"resource_type": "encrypt-resource",
		"resource": {
			"algorithm": "AEAD_AES_256_GCM",
			"ciphertext": "test_ciphertext_from_real_callback",
			"associated_data": "transaction",
			"nonce": "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ"
		},
		"summary": "支付成功"
	}`

	// 分析nonce
	analyzeNonce("AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ")

	// 发送测试请求
	err := sendRealNotifyTest(realNotifyData)
	if err != nil {
		fmt.Printf("❌ 测试失败: %v\n", err)
		return
	}

	fmt.Println("✅ 真实数据测试完成")
}

func analyzeNonce(nonceStr string) {
	fmt.Printf("\n--- Nonce分析 ---\n")
	fmt.Printf("原始Nonce: %s\n", nonceStr)
	fmt.Printf("Nonce长度: %d字符\n", len(nonceStr))

	// 尝试base64解码
	decoded, err := base64.StdEncoding.DecodeString(nonceStr)
	if err != nil {
		fmt.Printf("❌ Base64解码失败: %v\n", err)
		return
	}

	fmt.Printf("解码后长度: %d字节\n", len(decoded))
	fmt.Printf("解码后数据: %x\n", decoded)

	if len(decoded) == 12 {
		fmt.Println("✅ Nonce长度正确 (12字节)")
	} else {
		fmt.Printf("❌ Nonce长度错误: %d字节，期望12字节\n", len(decoded))
	}

	// 检查是否包含特殊字符
	fmt.Printf("是否包含特殊字符: ")
	hasSpecial := false
	for _, b := range decoded {
		if b < 32 || b > 126 {
			hasSpecial = true
			break
		}
	}
	if hasSpecial {
		fmt.Println("是")
	} else {
		fmt.Println("否")
	}
}

func sendRealNotifyTest(notifyData string) error {
	fmt.Printf("\n--- 发送真实回调测试 ---\n")

	// 目标URL
	url := "https://6b51df8a6941.ngrok-free.app/notify/wechat"

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(notifyData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头（模拟真实的微信支付回调）
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/4.0")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Encoding", "gzip")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")

	// 设置微信支付特有的头部
	req.Header.Set("Wechatpay-Nonce", "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ")
	req.Header.Set("Wechatpay-Serial", "PUB_KEY_ID_0112640559012025082200382286000401")
	req.Header.Set("Wechatpay-Signature", "test_signature_for_debugging")
	req.Header.Set("Wechatpay-Signature-Type", "WECHATPAY2-SHA256-RSA2048")
	req.Header.Set("Wechatpay-Timestamp", fmt.Sprintf("%d", time.Now().Unix()))

	fmt.Printf("请求URL: %s\n", url)
	fmt.Printf("请求数据: %s\n", notifyData)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(respBody))

	// 分析响应
	if resp.StatusCode == 200 {
		fmt.Println("✅ 回调处理成功")
	} else if resp.StatusCode == 500 {
		fmt.Println("❌ 服务器内部错误 - 可能是nonce问题")

		// 检查响应中是否包含nonce相关错误
		respStr := string(respBody)
		if contains(respStr, "nonce") || contains(respStr, "GCM") {
			fmt.Println("🔍 确认是nonce长度问题")
		}
	} else {
		fmt.Printf("⚠️ 其他错误，状态码: %d\n", resp.StatusCode)
	}

	return nil
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
