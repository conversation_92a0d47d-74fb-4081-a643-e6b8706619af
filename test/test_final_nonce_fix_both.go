package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 最终测试：验证支付和退款回调的nonce处理

func main() {
	fmt.Println("=== 最终Nonce修复验证测试 ===")

	// 测试支付回调
	fmt.Println("\n🔵 测试支付回调")
	err := testPaymentCallback()
	if err != nil {
		fmt.Printf("❌ 支付回调测试失败: %v\n", err)
	}

	// 等待一秒
	time.Sleep(1 * time.Second)

	// 测试退款回调
	fmt.Println("\n🟡 测试退款回调")
	err = testRefundCallback()
	if err != nil {
		fmt.Printf("❌ 退款回调测试失败: %v\n", err)
	}

	fmt.Println("\n✅ 最终nonce修复验证测试完成")
}

func testPaymentCallback() error {
	// 支付回调数据
	callbackData := map[string]interface{}{
		"id":            "EV-2018022511223320873",
		"create_time":   "2025-08-24T11:20:08+08:00",
		"resource_type": "encrypt-resource",
		"event_type":    "TRANSACTION.SUCCESS",
		"summary":       "支付成功",
		"resource": map[string]interface{}{
			"original_type":   "transaction",
			"algorithm":       "AEAD_AES_256_GCM",
			"ciphertext":      "test_payment_encrypted_data",
			"associated_data": "transaction",
			"nonce":           "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ", // 24字节nonce
		},
	}

	return sendCallback("/notify/wechat", callbackData, "支付回调")
}

func testRefundCallback() error {
	// 退款回调数据
	callbackData := map[string]interface{}{
		"id":            "2e2bc1c9-e4c4-51f9-b15f-bcca12b749d5",
		"create_time":   "2025-08-24T11:17:55+08:00",
		"resource_type": "encrypt-resource",
		"event_type":    "REFUND.SUCCESS",
		"summary":       "退款成功",
		"resource": map[string]interface{}{
			"original_type":   "refund",
			"algorithm":       "AEAD_AES_256_GCM",
			"ciphertext":      "test_refund_encrypted_data",
			"associated_data": "refund",
			"nonce":           "xl0OyL12rzsKkdTKUFvJCw1I49BV9vsb", // 24字节nonce
		},
	}

	return sendCallback("/notify/wechat/refund", callbackData, "退款回调")
}

func sendCallback(path string, callbackData map[string]interface{}, testType string) error {
	// 序列化数据
	jsonData, err := json.Marshal(callbackData)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %w", err)
	}

	// 目标URL
	url := "https://6b51df8a6941.ngrok-free.app" + path

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Encoding", "gzip")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("User-Agent", "Mozilla/4.0")

	// 设置微信支付V3的签名头
	nonce := callbackData["resource"].(map[string]interface{})["nonce"].(string)
	req.Header.Set("Wechatpay-Nonce", nonce)
	req.Header.Set("Wechatpay-Serial", "PUB_KEY_ID_0112640559012025082200382286000401")
	req.Header.Set("Wechatpay-Signature", "test_signature_for_final_verification")
	req.Header.Set("Wechatpay-Signature-Type", "WECHATPAY2-SHA256-RSA2048")
	req.Header.Set("Wechatpay-Timestamp", fmt.Sprintf("%d", time.Now().Unix()))

	fmt.Printf("📤 %s请求:\n", testType)
	fmt.Printf("  URL: %s\n", url)
	fmt.Printf("  Nonce: %s (长度: %d)\n", nonce, len(nonce))

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	fmt.Printf("📥 %s响应:\n", testType)
	fmt.Printf("  状态码: %d\n", resp.StatusCode)
	fmt.Printf("  响应内容: %s\n", string(respBody))

	// 分析结果
	fmt.Printf("🔍 %s结果分析:\n", testType)
	switch resp.StatusCode {
	case 200:
		fmt.Printf("  ✅ %s处理成功 - nonce问题已解决\n", testType)
	case 400:
		fmt.Printf("  ⚠️ %s返回400 - 业务逻辑问题，但nonce处理正常\n", testType)
	case 500:
		respStr := string(respBody)
		if contains(respStr, "nonce") || contains(respStr, "GCM") {
			fmt.Printf("  ❌ %s仍然有nonce问题\n", testType)
		} else {
			fmt.Printf("  ⚠️ %s其他500错误\n", testType)
		}
	default:
		fmt.Printf("  ⚠️ %s其他状态码: %d\n", testType, resp.StatusCode)
	}

	// 检查是否还有nonce长度错误
	if contains(string(respBody), "incorrect nonce length") {
		fmt.Printf("  ❌ %s仍然存在nonce长度问题\n", testType)
	} else {
		fmt.Printf("  ✅ %s没有nonce长度错误\n", testType)
	}

	return nil
}

func contains(s, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(s) < len(substr) {
		return false
	}
	
	for i := 0; i <= len(s)-len(substr); i++ {
		match := true
		for j := 0; j < len(substr); j++ {
			if s[i+j] != substr[j] {
				match = false
				break
			}
		}
		if match {
			return true
		}
	}
	return false
}
