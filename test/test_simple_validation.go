package main

import (
	"fmt"
	"time"

	"pay-core/pkg/lock"
	"pay-core/pkg/queue"
)

func main() {
	fmt.Println("=== 简单功能验证测试 ===")

	// 测试1: 验证分布式锁功能
	testLockFunctions()

	// 测试2: 验证队列任务功能
	testQueueFunctions()

	// 测试3: 验证常量定义
	testConstants()

	fmt.Println("\n=== 验证测试完成 ===")
	fmt.Println("✅ 所有核心功能编译通过，代码结构正确")
}

func testLockFunctions() {
	fmt.Println("\n--- 测试分布式锁功能 ---")

	// 测试锁键生成
	orderNo := "TEST_ORDER_123"
	refundNo := "TEST_REFUND_456"

	paymentLockKey := lock.PaymentLockKey(orderNo)
	refundLockKey := lock.RefundLockKey(refundNo)

	expectedPaymentKey := "payment_lock:TEST_ORDER_123"
	expectedRefundKey := "refund_lock:TEST_REFUND_456"

	if paymentLockKey == expectedPaymentKey {
		fmt.Printf("✅ 支付锁键生成正确: %s\n", paymentLockKey)
	} else {
		fmt.Printf("❌ 支付锁键生成错误: 期望 %s, 实际 %s\n", expectedPaymentKey, paymentLockKey)
	}

	if refundLockKey == expectedRefundKey {
		fmt.Printf("✅ 退款锁键生成正确: %s\n", refundLockKey)
	} else {
		fmt.Printf("❌ 退款锁键生成错误: 期望 %s, 实际 %s\n", expectedRefundKey, refundLockKey)
	}
}

func testQueueFunctions() {
	fmt.Println("\n--- 测试队列任务功能 ---")

	orderNo := "TEST_ORDER_123"
	refundNo := "TEST_REFUND_456"
	testTime := time.Now().Add(5 * time.Minute)

	// 测试订单任务创建
	orderExpireTask := queue.CreateOrderExpireTask(orderNo, testTime)
	orderPollTask := queue.CreateOrderPollTask(orderNo, testTime, 1)

	// 验证订单过期任务
	if orderExpireTask.ID == "expire_TEST_ORDER_123" &&
		orderExpireTask.Type == queue.TaskTypeOrderExpire {
		fmt.Printf("✅ 订单过期任务创建正确: %s\n", orderExpireTask.ID)
	} else {
		fmt.Printf("❌ 订单过期任务创建错误\n")
	}

	// 验证订单轮询任务
	if orderPollTask.ID == "poll_TEST_ORDER_123_1" &&
		orderPollTask.Type == queue.TaskTypeOrderPoll {
		fmt.Printf("✅ 订单轮询任务创建正确: %s\n", orderPollTask.ID)
	} else {
		fmt.Printf("❌ 订单轮询任务创建错误\n")
	}

	// 测试退款任务创建
	refundExpireTask := queue.CreateRefundExpireTask(refundNo, testTime)
	refundPollTask := queue.CreateRefundPollTask(refundNo, testTime, 2)

	// 验证退款过期任务
	if refundExpireTask.ID == "refund_expire_TEST_REFUND_456" &&
		refundExpireTask.Type == queue.TaskTypeRefundExpire {
		fmt.Printf("✅ 退款过期任务创建正确: %s\n", refundExpireTask.ID)
	} else {
		fmt.Printf("❌ 退款过期任务创建错误\n")
	}

	// 验证退款轮询任务
	if refundPollTask.ID == "refund_poll_TEST_REFUND_456_2" &&
		refundPollTask.Type == queue.TaskTypeRefundPoll {
		fmt.Printf("✅ 退款轮询任务创建正确: %s\n", refundPollTask.ID)
	} else {
		fmt.Printf("❌ 退款轮询任务创建错误\n")
	}
}

func testConstants() {
	fmt.Println("\n--- 测试常量定义 ---")

	// 验证任务类型常量
	constants := map[string]string{
		"订单过期": queue.TaskTypeOrderExpire,
		"订单轮询": queue.TaskTypeOrderPoll,
		"退款过期": queue.TaskTypeRefundExpire,
		"退款轮询": queue.TaskTypeRefundPoll,
	}

	expectedValues := map[string]string{
		"订单过期": "order_expire",
		"订单轮询": "order_poll",
		"退款过期": "refund_expire",
		"退款轮询": "refund_poll",
	}

	allCorrect := true
	for name, actual := range constants {
		expected := expectedValues[name]
		if actual == expected {
			fmt.Printf("✅ %s常量正确: %s\n", name, actual)
		} else {
			fmt.Printf("❌ %s常量错误: 期望 %s, 实际 %s\n", name, expected, actual)
			allCorrect = false
		}
	}

	if allCorrect {
		fmt.Printf("✅ 所有常量定义验证通过\n")
	}
}
