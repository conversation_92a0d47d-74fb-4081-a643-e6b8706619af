package main

import (
	"bytes"
	"context"
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/internal/repository"
	"pay-core/pkg/database"
	"pay-core/pkg/types"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// WechatV3NotifyRequest 微信支付V3回调请求结构
type WechatV3NotifyRequest struct {
	ID           string                 `json:"id"`
	CreateTime   string                 `json:"create_time"`
	EventType    string                 `json:"event_type"`
	ResourceType string                 `json:"resource_type"`
	Resource     WechatV3NotifyResource `json:"resource"`
	Summary      string                 `json:"summary"`
}

// WechatV3NotifyResource 微信支付V3回调资源结构
type WechatV3NotifyResource struct {
	Algorithm      string `json:"algorithm"`
	Ciphertext     string `json:"ciphertext"`
	AssociatedData string `json:"associated_data"`
	Nonce          string `json:"nonce"`
}

// WechatPaymentNotifyData 微信支付成功回调解密后的数据
type WechatPaymentNotifyData struct {
	AppID          string `json:"appid"`
	MchID          string `json:"mchid"`
	OutTradeNo     string `json:"out_trade_no"`
	TransactionID  string `json:"transaction_id"`
	TradeType      string `json:"trade_type"`
	TradeState     string `json:"trade_state"`
	TradeStateDesc string `json:"trade_state_desc"`
	BankType       string `json:"bank_type"`
	Attach         string `json:"attach"`
	SuccessTime    string `json:"success_time"`
	Payer          struct {
		OpenID string `json:"openid"`
	} `json:"payer"`
	Amount struct {
		Total         int    `json:"total"`
		PayerTotal    int    `json:"payer_total"`
		Currency      string `json:"currency"`
		PayerCurrency string `json:"payer_currency"`
	} `json:"amount"`
}

// WechatRefundNotifyData 微信退款成功回调解密后的数据
type WechatRefundNotifyData struct {
	MchID               string `json:"mchid"`
	OutTradeNo          string `json:"out_trade_no"`
	TransactionID       string `json:"transaction_id"`
	OutRefundNo         string `json:"out_refund_no"`
	RefundID            string `json:"refund_id"`
	RefundStatus        string `json:"refund_status"`
	SuccessTime         string `json:"success_time"`
	RecvAccount         string `json:"recv_account"`
	UserReceivedAccount string `json:"user_received_account"`
	Amount              struct {
		Total       int    `json:"total"`
		Refund      int    `json:"refund"`
		PayerTotal  int    `json:"payer_total"`
		PayerRefund int    `json:"payer_refund"`
		Currency    string `json:"currency"`
	} `json:"amount"`
}

func main() {
	fmt.Println("=== 微信支付回调测试 ===")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewConnection(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect database: %v", err)
	}

	// 创建测试订单
	testOrder := createTestOrder(db)
	fmt.Printf("创建测试订单: %s\n", testOrder.OrderNo)

	// 测试支付成功回调
	fmt.Println("\n1. 测试支付成功回调...")
	if err := testPaymentSuccessNotify(cfg, testOrder); err != nil {
		log.Printf("支付成功回调测试失败: %v", err)
	} else {
		fmt.Println("支付成功回调测试通过")
	}

	// 创建退款测试订单
	refundOrder := createRefundTestOrder(db, testOrder)
	fmt.Printf("\n创建退款测试订单: %s\n", refundOrder.RefundNo)

	// 测试退款成功回调
	fmt.Println("\n2. 测试退款成功回调...")
	if err := testRefundSuccessNotify(cfg, testOrder, refundOrder); err != nil {
		log.Printf("退款成功回调测试失败: %v", err)
	} else {
		fmt.Println("退款成功回调测试通过")
	}

	fmt.Println("\n=== 测试完成 ===")
}

// createTestOrder 创建测试订单
func createTestOrder(db *gorm.DB) *model.PaymentOrder {
	orderNo := fmt.Sprintf("TEST_NOTIFY_%d", time.Now().Unix())

	order := &model.PaymentOrder{
		OrderNo:     orderNo,
		AppID:       1, // 假设存在ID为1的应用
		AppOrderNo:  fmt.Sprintf("APP_%d", time.Now().Unix()),
		AppUserID:   "test_user_001",
		TotalAmount: decimal.NewFromFloat(100.00),
		Subject:     "微信支付回调测试订单",
		Body:        "这是一个用于测试微信支付回调的订单",
		PayChannel:  "wechat",
		Status:      model.OrderStatusWaitingPay,
		ExpireTime:  types.Time{Time: time.Now().Add(30 * time.Minute)},
	}

	// 保存到数据库
	orderRepo := repository.NewPaymentOrderRepository(db)
	if err := orderRepo.Create(context.Background(), order); err != nil {
		log.Printf("Failed to create test order: %v", err)
	}

	return order
}

// createRefundTestOrder 创建退款测试订单
func createRefundTestOrder(db *gorm.DB, paymentOrder *model.PaymentOrder) *model.RefundOrder {
	refundNo := fmt.Sprintf("REFUND_%d", time.Now().Unix())

	refundOrder := &model.RefundOrder{
		RefundNo:     refundNo,
		OrderNo:      paymentOrder.OrderNo,
		AppID:        paymentOrder.AppID,
		RefundAmount: decimal.NewFromFloat(50.00), // 部分退款
		Reason:       "测试退款",
		Status:       model.RefundStatusProcessing,
	}

	// 保存到数据库
	refundRepo := repository.NewRefundOrderRepository(db)
	if err := refundRepo.Create(context.Background(), refundOrder); err != nil {
		log.Printf("Failed to create refund order: %v", err)
	}

	return refundOrder
}

// testPaymentSuccessNotify 测试支付成功回调
func testPaymentSuccessNotify(cfg *config.Config, order *model.PaymentOrder) error {
	// 构造支付成功回调数据
	paymentData := WechatPaymentNotifyData{
		AppID:          cfg.Payment.Wechat.AppID,
		MchID:          cfg.Payment.Wechat.MchID,
		OutTradeNo:     order.OrderNo,
		TransactionID:  fmt.Sprintf("wx_%d", time.Now().Unix()),
		TradeType:      "NATIVE",
		TradeState:     "SUCCESS",
		TradeStateDesc: "支付成功",
		BankType:       "CMC",
		Attach:         order.Attach,
		SuccessTime:    time.Now().Format("2006-01-02T15:04:05+08:00"),
		Payer: struct {
			OpenID string `json:"openid"`
		}{
			OpenID: "test_openid_123",
		},
		Amount: struct {
			Total         int    `json:"total"`
			PayerTotal    int    `json:"payer_total"`
			Currency      string `json:"currency"`
			PayerCurrency string `json:"payer_currency"`
		}{
			Total:         int(order.TotalAmount.Mul(decimal.NewFromInt(100)).IntPart()), // 转换为分
			PayerTotal:    int(order.TotalAmount.Mul(decimal.NewFromInt(100)).IntPart()),
			Currency:      "CNY",
			PayerCurrency: "CNY",
		},
	}

	// 构造回调请求
	notifyReq := WechatV3NotifyRequest{
		ID:           fmt.Sprintf("notify_%d", time.Now().Unix()),
		CreateTime:   time.Now().Format("2006-01-02T15:04:05+08:00"),
		EventType:    "TRANSACTION.SUCCESS",
		ResourceType: "encrypt-resource",
		Summary:      "支付成功",
	}

	// 加密数据
	encryptedResource, err := encryptNotifyData(paymentData, cfg.Payment.Wechat.APIv3Key)
	if err != nil {
		return fmt.Errorf("failed to encrypt notify data: %w", err)
	}
	notifyReq.Resource = *encryptedResource

	// 发送回调请求
	return sendNotifyRequest("http://localhost:8080/notify/wechat", notifyReq, cfg)
}

// testRefundSuccessNotify 测试退款成功回调
func testRefundSuccessNotify(cfg *config.Config, order *model.PaymentOrder, refundOrder *model.RefundOrder) error {
	// 构造退款成功回调数据
	refundData := WechatRefundNotifyData{
		MchID:               cfg.Payment.Wechat.MchID,
		OutTradeNo:          order.OrderNo,
		TransactionID:       fmt.Sprintf("wx_%d", time.Now().Unix()-100),
		OutRefundNo:         refundOrder.RefundNo,
		RefundID:            fmt.Sprintf("wx_refund_%d", time.Now().Unix()),
		RefundStatus:        "SUCCESS",
		SuccessTime:         time.Now().Format("2006-01-02T15:04:05+08:00"),
		RecvAccount:         "招商银行信用卡0403",
		UserReceivedAccount: "招商银行信用卡0403",
		Amount: struct {
			Total       int    `json:"total"`
			Refund      int    `json:"refund"`
			PayerTotal  int    `json:"payer_total"`
			PayerRefund int    `json:"payer_refund"`
			Currency    string `json:"currency"`
		}{
			Total:       int(order.TotalAmount.Mul(decimal.NewFromInt(100)).IntPart()),
			Refund:      int(refundOrder.RefundAmount.Mul(decimal.NewFromInt(100)).IntPart()),
			PayerTotal:  int(order.TotalAmount.Mul(decimal.NewFromInt(100)).IntPart()),
			PayerRefund: int(refundOrder.RefundAmount.Mul(decimal.NewFromInt(100)).IntPart()),
			Currency:    "CNY",
		},
	}

	// 构造回调请求
	notifyReq := WechatV3NotifyRequest{
		ID:           fmt.Sprintf("refund_notify_%d", time.Now().Unix()),
		CreateTime:   time.Now().Format("2006-01-02T15:04:05+08:00"),
		EventType:    "REFUND.SUCCESS",
		ResourceType: "encrypt-resource",
		Summary:      "退款成功",
	}

	// 加密数据
	encryptedResource, err := encryptNotifyData(refundData, cfg.Payment.Wechat.APIv3Key)
	if err != nil {
		return fmt.Errorf("failed to encrypt refund notify data: %w", err)
	}
	notifyReq.Resource = *encryptedResource

	// 发送回调请求
	return sendNotifyRequest("http://localhost:8080/notify/wechat", notifyReq, cfg)
}

// encryptNotifyData 使用AES-256-GCM加密回调数据
func encryptNotifyData(data interface{}, apiKey string) (*WechatV3NotifyResource, error) {
	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	// 生成随机nonce (12字节)
	nonce := make([]byte, 12)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(apiKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// 设置associated_data
	associatedData := "transaction"

	// 加密数据
	ciphertext := gcm.Seal(nil, nonce, jsonData, []byte(associatedData))

	return &WechatV3NotifyResource{
		Algorithm:      "AEAD_AES_256_GCM",
		Ciphertext:     base64.StdEncoding.EncodeToString(ciphertext),
		AssociatedData: associatedData,
		Nonce:          base64.StdEncoding.EncodeToString(nonce),
	}, nil
}

// sendNotifyRequest 发送回调请求
func sendNotifyRequest(url string, notifyReq WechatV3NotifyRequest, cfg *config.Config) error {
	// 序列化请求数据
	jsonData, err := json.Marshal(notifyReq)
	if err != nil {
		return fmt.Errorf("failed to marshal notify request: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := generateWechatNonce()

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Wechatpay-Timestamp", timestamp)
	req.Header.Set("Wechatpay-Nonce", nonce)
	req.Header.Set("Wechatpay-Serial", cfg.Payment.Wechat.SerialNo)

	// 生成签名
	signature, err := generateSignature(req.Method, req.URL.Path, timestamp, nonce, string(jsonData), cfg)
	if err != nil {
		return fmt.Errorf("failed to generate signature: %w", err)
	}
	req.Header.Set("Wechatpay-Signature", signature)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	fmt.Printf("回调响应状态: %d\n", resp.StatusCode)
	fmt.Printf("回调响应内容: %s\n", string(respBody))

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("callback failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	return nil
}

// generateSignature 生成微信支付V3签名
func generateSignature(method, path, timestamp, nonce, body string, cfg *config.Config) (string, error) {
	// 构建签名字符串
	signStr := fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n", method, path, timestamp, nonce, body)

	// 读取私钥
	var privateKeyContent string
	if cfg.Payment.Wechat.PrivateKeyPath != "" {
		keyBytes, err := os.ReadFile(cfg.Payment.Wechat.PrivateKeyPath)
		if err != nil {
			return "", fmt.Errorf("failed to read private key file: %w", err)
		}
		privateKeyContent = string(keyBytes)
	} else {
		privateKeyContent = cfg.Payment.Wechat.PrivateKey
	}

	// 解析私钥
	block, _ := pem.Decode([]byte(privateKeyContent))
	if block == nil {
		return "", fmt.Errorf("failed to decode private key")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		// 尝试解析PKCS1格式
		privateKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return "", fmt.Errorf("failed to parse private key: %w", err)
		}
	}

	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return "", fmt.Errorf("private key is not RSA key")
	}

	// 计算SHA256哈希
	hash := sha256.Sum256([]byte(signStr))

	// 使用RSA-PSS签名
	signature, err := rsa.SignPSS(rand.Reader, rsaPrivateKey, crypto.SHA256, hash[:], nil)
	if err != nil {
		return "", fmt.Errorf("failed to sign: %w", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// generateWechatNonce 生成随机字符串
func generateWechatNonce() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 32)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}
