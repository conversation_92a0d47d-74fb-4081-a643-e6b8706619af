package main

import (
	"encoding/base64"
	"fmt"
)

func main() {
	fmt.Println("=== 正确的Nonce处理方式验证 ===")

	// 真实的微信nonce（base64编码）
	realNonceBase64 := "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ"
	fmt.Printf("原始nonce(base64): %s\n", realNonceBase64)
	fmt.Printf("原始长度: %d字符\n", len(realNonceBase64))

	// Base64解码
	nonceDecoded, err := base64.StdEncoding.DecodeString(realNonceBase64)
	if err != nil {
		fmt.Printf("❌ Base64解码失败: %v\n", err)
		return
	}

	fmt.Printf("解码后长度: %d字节\n", len(nonceDecoded))
	fmt.Printf("解码后数据(hex): %x\n", nonceDecoded)

	// 问题分析
	fmt.Printf("\n--- 问题分析 ---\n")
	fmt.Printf("AES-GCM要求: 12字节nonce\n")
	fmt.Printf("微信发送: %d字节nonce\n", len(nonceDecoded))
	fmt.Printf("gopay库V3DecryptPayNotifyCipherText期望: 字符串形式的nonce\n")

	// 解决方案
	fmt.Printf("\n--- 解决方案 ---\n")

	if len(nonceDecoded) == 24 {
		// 方案1: 截取前12字节
		nonce12 := nonceDecoded[:12]
		fmt.Printf("截取前12字节: %x\n", nonce12)
		fmt.Printf("转换为字符串长度: %d\n", len(string(nonce12)))

		// 方案2: 重新base64编码前12字节
		nonce12Base64 := base64.StdEncoding.EncodeToString(nonce12)
		fmt.Printf("前12字节重新编码: %s\n", nonce12Base64)

		// 验证重新编码的结果
		reDecoded, _ := base64.StdEncoding.DecodeString(nonce12Base64)
		fmt.Printf("重新解码长度: %d字节\n", len(reDecoded))
		fmt.Printf("重新解码数据: %x\n", reDecoded)

		if len(reDecoded) == 12 {
			fmt.Println("✅ 重新编码的nonce长度正确")
		} else {
			fmt.Println("❌ 重新编码的nonce长度错误")
		}
	}

	// 测试gopay库期望的格式
	fmt.Printf("\n--- gopay库期望格式测试 ---\n")
	
	// gopay的V3DecryptPayNotifyCipherText函数使用 []byte(nonce)
	// 这意味着它期望nonce是一个字符串，然后转换为字节数组
	
	// 如果我们直接使用base64字符串
	directBytes := []byte(realNonceBase64)
	fmt.Printf("直接转换base64字符串: %d字节\n", len(directBytes))
	
	// 如果我们使用解码后的字节作为字符串
	decodedAsString := string(nonceDecoded)
	decodedAsStringBytes := []byte(decodedAsString)
	fmt.Printf("解码后作为字符串再转换: %d字节\n", len(decodedAsStringBytes))
	
	// 如果我们使用截取的12字节作为字符串
	if len(nonceDecoded) >= 12 {
		truncated12 := nonceDecoded[:12]
		truncated12AsString := string(truncated12)
		truncated12AsStringBytes := []byte(truncated12AsString)
		fmt.Printf("截取12字节作为字符串再转换: %d字节\n", len(truncated12AsStringBytes))
		
		if len(truncated12AsStringBytes) == 12 {
			fmt.Println("✅ 截取12字节的方案可行")
		} else {
			fmt.Println("❌ 截取12字节的方案不可行")
		}
	}
}
