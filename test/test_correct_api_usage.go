package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 测试使用正确的gopay API

func main() {
	fmt.Println("=== 测试使用正确的gopay API ===")

	// 使用真实的微信支付回调数据结构
	realCallbackData := map[string]interface{}{
		"id":            "EV-2018022511223320873",
		"create_time":   "2025-08-24T10:42:40+08:00",
		"resource_type": "encrypt-resource",
		"event_type":    "TRANSACTION.SUCCESS",
		"summary":       "支付成功",
		"resource": map[string]interface{}{
			"original_type":   "transaction",
			"algorithm":       "AEAD_AES_256_GCM",
			"ciphertext":      "test_encrypted_data_for_api_verification",
			"associated_data": "transaction",
			"nonce":           "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ", // 真实的微信nonce
		},
	}

	fmt.Printf("使用的API: wechat.V3DecryptNotifyCipherTextToStruct() (推荐)\n")
	fmt.Printf("替代了: notifyReq.DecryptPayCipherText() 和 wechat.V3DecryptPayNotifyCipherText()\n")

	// 发送测试请求
	err := sendAPITest(realCallbackData)
	if err != nil {
		fmt.Printf("❌ 测试失败: %v\n", err)
		return
	}

	fmt.Println("✅ 正确API使用测试完成")
}

func sendAPITest(callbackData map[string]interface{}) error {
	fmt.Printf("\n--- 发送API测试 ---\n")

	// 序列化数据
	jsonData, err := json.Marshal(callbackData)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %w", err)
	}

	// 目标URL
	url := "https://6b51df8a6941.ngrok-free.app/notify/wechat"

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置真实的请求头
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Encoding", "gzip")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("User-Agent", "Mozilla/4.0")

	// 设置微信支付V3的签名头（使用真实数据）
	req.Header.Set("Wechatpay-Nonce", "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ")
	req.Header.Set("Wechatpay-Serial", "PUB_KEY_ID_0112640559012025082200382286000401")
	req.Header.Set("Wechatpay-Signature", "test_signature_for_api_verification")
	req.Header.Set("Wechatpay-Signature-Type", "WECHATPAY2-SHA256-RSA2048")
	req.Header.Set("Wechatpay-Timestamp", "1756003360")

	fmt.Printf("📤 请求信息:\n")
	fmt.Printf("  URL: %s\n", url)
	fmt.Printf("  使用API: V3DecryptNotifyCipherTextToStruct\n")
	fmt.Printf("  Nonce: %s\n", req.Header.Get("Wechatpay-Nonce"))

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	fmt.Printf("\n📥 响应结果:\n")
	fmt.Printf("  状态码: %d\n", resp.StatusCode)
	fmt.Printf("  响应内容: %s\n", string(respBody))

	// 分析结果
	fmt.Printf("\n🔍 结果分析:\n")
	switch resp.StatusCode {
	case 200:
		fmt.Println("  ✅ 回调处理成功 - API使用正确")
	case 500:
		respStr := string(respBody)
		if contains(respStr, "nonce") || contains(respStr, "GCM") {
			fmt.Println("  ❌ 仍然是nonce相关问题")
			fmt.Println("  🔍 可能需要检查gopay库版本或其他配置")
		} else if contains(respStr, "decrypt") {
			fmt.Println("  ❌ 解密相关问题")
			fmt.Println("  🔍 可能是ciphertext或其他参数问题")
		} else {
			fmt.Println("  ⚠️ 其他500错误（可能是业务逻辑问题）")
		}
	default:
		fmt.Printf("  ⚠️ 其他状态码: %d\n", resp.StatusCode)
	}

	// 检查是否还有nonce长度错误
	if contains(string(respBody), "incorrect nonce length") {
		fmt.Println("  ❌ 仍然存在nonce长度问题")
		fmt.Println("  🔍 建议检查gopay库版本和V3DecryptNotifyCipherTextToStruct实现")
	} else {
		fmt.Println("  ✅ 没有nonce长度错误")
	}

	return nil
}

func contains(s, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(s) < len(substr) {
		return false
	}
	
	for i := 0; i <= len(s)-len(substr); i++ {
		match := true
		for j := 0; j < len(substr); j++ {
			if s[i+j] != substr[j] {
				match = false
				break
			}
		}
		if match {
			return true
		}
	}
	return false
}
