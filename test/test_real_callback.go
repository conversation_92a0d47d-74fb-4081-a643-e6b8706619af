package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 使用真实的微信支付回调数据测试付款通知接口

func main() {
	fmt.Println("=== 使用真实微信支付回调数据测试 ===")

	// 从日志中提取的真实回调数据（Content-Length: 915）
	realCallbackData := `{
		"id": "EV-2018022511223320873",
		"create_time": "2025-08-24T10:42:40+08:00",
		"resource_type": "encrypt-resource",
		"event_type": "TRANSACTION.SUCCESS",
		"summary": "支付成功",
		"resource": {
			"original_type": "transaction",
			"algorithm": "AEAD_AES_256_GCM",
			"ciphertext": "sRvt...encrypted_data_here...==",
			"associated_data": "transaction",
			"nonce": "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ"
		}
	}`

	// 测试回调接口
	err := testRealCallback(realCallbackData)
	if err != nil {
		fmt.Printf("❌ 测试失败: %v\n", err)
		return
	}

	fmt.Println("✅ 真实回调数据测试完成")
}

func testRealCallback(callbackData string) error {
	fmt.Printf("\n--- 发送真实回调数据 ---\n")

	// 目标URL（使用您配置的ngrok地址）
	url := "https://6b51df8a6941.ngrok-free.app/notify/wechat"

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(callbackData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置真实的请求头（从日志中提取）
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Encoding", "gzip")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Content-Length", "915")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("User-Agent", "Mozilla/4.0")

	// 设置微信支付V3的签名头（从真实回调中提取）
	req.Header.Set("Wechatpay-Nonce", "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ")
	req.Header.Set("Wechatpay-Serial", "PUB_KEY_ID_0112640559012025082200382286000401")
	req.Header.Set("Wechatpay-Signature", "bKR17cAGSfpB2LU0sPphcrkxcZ205Ll5aR4oEnp+vrSkoGRT/uQpTt8ZtPgLsk+WiktZgclF+duIvYRODSBMbAPS+Z04k0cvK8nX3ePYyq02dJCrwd0kDekKw909NSAMxhjmBWUpdMdvWBhxOJfqvRNUqkSxqgXJlR3BBRnJ/4mufmBHhUxqF8w0V2ywdEveh1B6yG3JV/29B2AMRaPD3C/VlNyO5y+nse2Hbn4tVgk8RtHq/yVy+Y5UkPPdOtuYtHp+YsSTIK1M2OHMpplkz9rfnYEQVZvdZrWFb5bB8qljbMuXKoen4pdKDYaNSNd+6m/RGVjn9ZF38UFuHkJewA==")
	req.Header.Set("Wechatpay-Signature-Type", "WECHATPAY2-SHA256-RSA2048")
	req.Header.Set("Wechatpay-Timestamp", "1756003360")

	// 设置ngrok相关的头部
	req.Header.Set("X-Forwarded-For", "**************")
	req.Header.Set("X-Forwarded-Host", "6b51df8a6941.ngrok-free.app")
	req.Header.Set("X-Forwarded-Proto", "https")

	fmt.Printf("📤 请求信息:\n")
	fmt.Printf("  URL: %s\n", url)
	fmt.Printf("  Method: POST\n")
	fmt.Printf("  Content-Type: %s\n", req.Header.Get("Content-Type"))
	fmt.Printf("  Content-Length: %s\n", req.Header.Get("Content-Length"))
	fmt.Printf("  Wechatpay-Nonce: %s\n", req.Header.Get("Wechatpay-Nonce"))
	fmt.Printf("  Wechatpay-Serial: %s\n", req.Header.Get("Wechatpay-Serial"))
	fmt.Printf("  Wechatpay-Timestamp: %s\n", req.Header.Get("Wechatpay-Timestamp"))

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	fmt.Printf("\n🚀 发送请求...\n")
	start := time.Now()

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	duration := time.Since(start)

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	fmt.Printf("\n📥 响应信息:\n")
	fmt.Printf("  状态码: %d\n", resp.StatusCode)
	fmt.Printf("  响应时间: %v\n", duration)
	fmt.Printf("  响应长度: %d字节\n", len(respBody))
	fmt.Printf("  响应内容: %s\n", string(respBody))

	// 分析响应结果
	fmt.Printf("\n🔍 结果分析:\n")
	switch resp.StatusCode {
	case 200:
		fmt.Println("  ✅ 回调处理成功")
		if string(respBody) == "SUCCESS" {
			fmt.Println("  ✅ 返回了微信支付期望的SUCCESS响应")
		}
	case 500:
		fmt.Println("  ❌ 服务器内部错误")
		respStr := string(respBody)
		if containsAny(respStr, []string{"nonce", "GCM", "cipher"}) {
			fmt.Println("  🔍 确认是加密解密相关问题")
		}
		if containsAny(respStr, []string{"panic", "recovered"}) {
			fmt.Println("  🔍 服务器发生了panic")
		}
	case 400:
		fmt.Println("  ⚠️ 请求参数错误")
	case 401:
		fmt.Println("  ⚠️ 认证失败")
	default:
		fmt.Printf("  ⚠️ 其他错误，状态码: %d\n", resp.StatusCode)
	}

	// 检查响应头
	fmt.Printf("\n📋 响应头信息:\n")
	for name, values := range resp.Header {
		for _, value := range values {
			fmt.Printf("  %s: %s\n", name, value)
		}
	}

	return nil
}

// containsAny 检查字符串是否包含任意一个子字符串
func containsAny(s string, substrs []string) bool {
	for _, substr := range substrs {
		if contains(s, substr) {
			return true
		}
	}
	return false
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr)
}

// findSubstring 查找子字符串
func findSubstring(s, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(s) < len(substr) {
		return false
	}
	
	for i := 0; i <= len(s)-len(substr); i++ {
		match := true
		for j := 0; j < len(substr); j++ {
			if s[i+j] != substr[j] {
				match = false
				break
			}
		}
		if match {
			return true
		}
	}
	return false
}
