package main

import (
	"encoding/base64"
	"fmt"
)

// 分析真实的nonce数据

func main() {
	fmt.Println("=== 真实Nonce数据分析 ===")

	// 从真实回调中提取的nonce
	realNonce := "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ"

	fmt.Printf("原始Nonce: %s\n", realNonce)
	fmt.Printf("原始长度: %d字符\n", len(realNonce))

	// Base64解码
	decoded, err := base64.StdEncoding.DecodeString(realNonce)
	if err != nil {
		fmt.Printf("❌ Base64解码失败: %v\n", err)
		return
	}

	fmt.Printf("解码后长度: %d字节\n", len(decoded))
	fmt.Printf("解码后数据(hex): %x\n", decoded)

	// 分析nonce长度问题
	if len(decoded) == 12 {
		fmt.Println("✅ Nonce长度正确 (12字节)")
	} else if len(decoded) == 24 {
		fmt.Println("⚠️ Nonce长度为24字节，需要截取")
		
		// 尝试不同的截取方式
		fmt.Println("\n--- 截取方案分析 ---")
		
		// 方案1: 前12字节
		first12 := decoded[:12]
		first12Encoded := base64.StdEncoding.EncodeToString(first12)
		fmt.Printf("前12字节: %x\n", first12)
		fmt.Printf("前12字节编码: %s\n", first12Encoded)
		
		// 方案2: 后12字节
		last12 := decoded[12:]
		last12Encoded := base64.StdEncoding.EncodeToString(last12)
		fmt.Printf("后12字节: %x\n", last12)
		fmt.Printf("后12字节编码: %s\n", last12Encoded)
		
		// 方案3: 中间12字节（如果有的话）
		if len(decoded) >= 18 {
			middle12 := decoded[6:18]
			middle12Encoded := base64.StdEncoding.EncodeToString(middle12)
			fmt.Printf("中间12字节: %x\n", middle12)
			fmt.Printf("中间12字节编码: %s\n", middle12Encoded)
		}
		
	} else {
		fmt.Printf("❌ Nonce长度异常: %d字节\n", len(decoded))
	}

	// 检查是否包含特殊模式
	fmt.Println("\n--- 数据模式分析 ---")
	
	// 检查是否有重复模式
	if len(decoded) == 24 {
		first12 := decoded[:12]
		last12 := decoded[12:]
		
		fmt.Printf("前12字节: %x\n", first12)
		fmt.Printf("后12字节: %x\n", last12)
		
		// 检查是否相同
		same := true
		for i := 0; i < 12; i++ {
			if first12[i] != last12[i] {
				same = false
				break
			}
		}
		
		if same {
			fmt.Println("🔍 前后12字节相同，可能是重复数据")
		} else {
			fmt.Println("🔍 前后12字节不同，可能是连续数据")
		}
		
		// 检查是否有明显的分界
		fmt.Printf("分界点数据: %02x %02x | %02x %02x\n", 
			decoded[10], decoded[11], decoded[12], decoded[13])
	}

	// 生成测试用的12字节nonce
	fmt.Println("\n--- 生成测试Nonce ---")
	if len(decoded) >= 12 {
		testNonce := decoded[:12]
		testNonceEncoded := base64.StdEncoding.EncodeToString(testNonce)
		fmt.Printf("测试用Nonce(前12字节): %s\n", testNonceEncoded)
		fmt.Printf("测试用Nonce长度: %d字节\n", len(testNonce))
	}
}
