package main

import (
	"bytes"
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	cryptorand "crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"os"
	"strconv"
	"time"
)

// 微信支付V3回调测试
// 基于微信支付官方文档构造回调数据

// WechatV3NotifyRequest 微信支付V3回调请求结构
type WechatV3NotifyRequest struct {
	ID           string                 `json:"id"`
	CreateTime   string                 `json:"create_time"`
	EventType    string                 `json:"event_type"`
	ResourceType string                 `json:"resource_type"`
	Resource     WechatV3NotifyResource `json:"resource"`
	Summary      string                 `json:"summary"`
}

// WechatV3NotifyResource 微信支付V3回调资源结构
type WechatV3NotifyResource struct {
	Algorithm      string `json:"algorithm"`
	Ciphertext     string `json:"ciphertext"`
	AssociatedData string `json:"associated_data"`
	Nonce          string `json:"nonce"`
}

// WechatPaymentNotifyData 微信支付成功回调解密后的数据
type WechatPaymentNotifyData struct {
	AppID          string `json:"appid"`
	MchID          string `json:"mchid"`
	OutTradeNo     string `json:"out_trade_no"`
	TransactionID  string `json:"transaction_id"`
	TradeType      string `json:"trade_type"`
	TradeState     string `json:"trade_state"`
	TradeStateDesc string `json:"trade_state_desc"`
	BankType       string `json:"bank_type"`
	Attach         string `json:"attach"`
	SuccessTime    string `json:"success_time"`
	Payer          struct {
		OpenID string `json:"openid"`
	} `json:"payer"`
	Amount struct {
		Total         int    `json:"total"`
		PayerTotal    int    `json:"payer_total"`
		Currency      string `json:"currency"`
		PayerCurrency string `json:"payer_currency"`
	} `json:"amount"`
}

func main() {
	fmt.Println("=== 微信支付回调测试 ===")

	// 配置信息（从配置文件读取）
	config := map[string]string{
		"app_id":           "wxcf9a42f63c23a637",
		"mch_id":           "**********",
		"apiv3_key":        "ZxQr5VpJ9sW4nGfYhLdK8cT3bRm7eNg2",
		"serial_no":        "7042C24F0C04F7DEC91393E505163FCA3170B46B",
		"private_key_path": "configs/**********_20250822_cert/apiclient_key.pem",
	}

	// 测试订单号（可以是已存在的订单号）
	testOrderNo := fmt.Sprintf("TEST_NOTIFY_%d", time.Now().Unix())

	// 1. 测试支付成功回调
	fmt.Println("\n1. 测试支付成功回调...")
	if err := testPaymentNotify(config, testOrderNo); err != nil {
		log.Printf("支付回调测试失败: %v", err)
	} else {
		fmt.Println("支付回调测试成功")
	}

	// 2. 测试退款成功回调
	fmt.Println("\n2. 测试退款成功回调...")
	refundNo := fmt.Sprintf("REFUND_%d", time.Now().Unix())
	if err := testRefundNotify(config, testOrderNo, refundNo); err != nil {
		log.Printf("退款回调测试失败: %v", err)
	} else {
		fmt.Println("退款回调测试成功")
	}

	fmt.Println("\n=== 测试完成 ===")
}

// testPaymentNotify 测试支付成功回调
func testPaymentNotify(config map[string]string, orderNo string) error {
	// 构造支付成功回调数据
	paymentData := WechatPaymentNotifyData{
		AppID:          config["app_id"],
		MchID:          config["mch_id"],
		OutTradeNo:     orderNo,
		TransactionID:  fmt.Sprintf("wx_%d", time.Now().Unix()),
		TradeType:      "NATIVE",
		TradeState:     "SUCCESS",
		TradeStateDesc: "支付成功",
		BankType:       "CMC",
		Attach:         "",
		SuccessTime:    time.Now().Format("2006-01-02T15:04:05+08:00"),
		Payer: struct {
			OpenID string `json:"openid"`
		}{
			OpenID: "test_openid_123",
		},
		Amount: struct {
			Total         int    `json:"total"`
			PayerTotal    int    `json:"payer_total"`
			Currency      string `json:"currency"`
			PayerCurrency string `json:"payer_currency"`
		}{
			Total:         10000, // 100.00元，单位分
			PayerTotal:    10000,
			Currency:      "CNY",
			PayerCurrency: "CNY",
		},
	}

	// 构造回调请求
	notifyReq := WechatV3NotifyRequest{
		ID:           fmt.Sprintf("notify_%d", time.Now().Unix()),
		CreateTime:   time.Now().Format("2006-01-02T15:04:05+08:00"),
		EventType:    "TRANSACTION.SUCCESS",
		ResourceType: "encrypt-resource",
		Summary:      "支付成功",
	}

	// 加密数据
	encryptedResource, err := encryptNotifyData(paymentData, config["apiv3_key"])
	if err != nil {
		return fmt.Errorf("failed to encrypt notify data: %w", err)
	}
	notifyReq.Resource = *encryptedResource

	// 发送回调请求
	return sendNotifyRequest("http://localhost:8080/notify/wechat", notifyReq, config)
}

// testRefundNotify 测试退款成功回调
func testRefundNotify(config map[string]string, orderNo, refundNo string) error {
	// 构造退款成功回调数据
	refundData := map[string]interface{}{
		"mchid":                 config["mch_id"],
		"out_trade_no":          orderNo,
		"transaction_id":        fmt.Sprintf("wx_%d", time.Now().Unix()-100),
		"out_refund_no":         refundNo,
		"refund_id":             fmt.Sprintf("wx_refund_%d", time.Now().Unix()),
		"refund_status":         "SUCCESS",
		"success_time":          time.Now().Format("2006-01-02T15:04:05+08:00"),
		"recv_account":          "招商银行信用卡0403",
		"user_received_account": "招商银行信用卡0403",
		"amount": map[string]interface{}{
			"total":        10000, // 原订单金额
			"refund":       5000,  // 退款金额
			"payer_total":  10000,
			"payer_refund": 5000,
			"currency":     "CNY",
		},
	}

	// 构造回调请求
	notifyReq := WechatV3NotifyRequest{
		ID:           fmt.Sprintf("refund_notify_%d", time.Now().Unix()),
		CreateTime:   time.Now().Format("2006-01-02T15:04:05+08:00"),
		EventType:    "REFUND.SUCCESS",
		ResourceType: "encrypt-resource",
		Summary:      "退款成功",
	}

	// 加密数据
	encryptedResource, err := encryptNotifyData(refundData, config["apiv3_key"])
	if err != nil {
		return fmt.Errorf("failed to encrypt refund notify data: %w", err)
	}
	notifyReq.Resource = *encryptedResource

	// 发送回调请求
	return sendNotifyRequest("http://localhost:8080/notify/wechat", notifyReq, config)
}

// encryptNotifyData 使用AES-256-GCM加密回调数据
func encryptNotifyData(data interface{}, apiKey string) (*WechatV3NotifyResource, error) {
	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	fmt.Printf("原始数据: %s\n", string(jsonData))

	// 生成随机nonce (12字节)
	nonce := make([]byte, 12)
	if _, err := io.ReadFull(cryptorand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(apiKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// 设置associated_data
	associatedData := "transaction"

	// 加密数据
	ciphertext := gcm.Seal(nil, nonce, jsonData, []byte(associatedData))

	return &WechatV3NotifyResource{
		Algorithm:      "AEAD_AES_256_GCM",
		Ciphertext:     base64.StdEncoding.EncodeToString(ciphertext),
		AssociatedData: associatedData,
		Nonce:          base64.StdEncoding.EncodeToString(nonce),
	}, nil
}

// sendNotifyRequest 发送回调请求
func sendNotifyRequest(url string, notifyReq WechatV3NotifyRequest, config map[string]string) error {
	// 序列化请求数据
	jsonData, err := json.Marshal(notifyReq)
	if err != nil {
		return fmt.Errorf("failed to marshal notify request: %w", err)
	}

	fmt.Printf("回调请求数据: %s\n", string(jsonData))

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := generateRandomString(32)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Wechatpay-Timestamp", timestamp)
	req.Header.Set("Wechatpay-Nonce", nonce)
	req.Header.Set("Wechatpay-Serial", config["serial_no"])

	// 生成签名
	signature, err := generateSignature(req.Method, req.URL.Path, timestamp, nonce, string(jsonData), config)
	if err != nil {
		return fmt.Errorf("failed to generate signature: %w", err)
	}
	req.Header.Set("Wechatpay-Signature", signature)

	fmt.Printf("请求头信息:\n")
	fmt.Printf("  Wechatpay-Timestamp: %s\n", timestamp)
	fmt.Printf("  Wechatpay-Nonce: %s\n", nonce)
	fmt.Printf("  Wechatpay-Serial: %s\n", config["serial_no"])
	fmt.Printf("  Wechatpay-Signature: %s\n", signature)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	fmt.Printf("回调响应状态: %d\n", resp.StatusCode)
	fmt.Printf("回调响应内容: %s\n", string(respBody))

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("callback failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	return nil
}

// generateSignature 生成微信支付V3签名
func generateSignature(method, path, timestamp, nonce, body string, config map[string]string) (string, error) {
	// 构建签名字符串
	signStr := fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n", method, path, timestamp, nonce, body)

	fmt.Printf("签名字符串: %q\n", signStr)

	// 读取私钥
	keyBytes, err := os.ReadFile(config["private_key_path"])
	if err != nil {
		return "", fmt.Errorf("failed to read private key file: %w", err)
	}

	// 解析私钥
	block, _ := pem.Decode(keyBytes)
	if block == nil {
		return "", fmt.Errorf("failed to decode private key")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		// 尝试解析PKCS1格式
		privateKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return "", fmt.Errorf("failed to parse private key: %w", err)
		}
	}

	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return "", fmt.Errorf("private key is not RSA key")
	}

	// 计算SHA256哈希
	hash := sha256.Sum256([]byte(signStr))

	// 使用RSA-PSS签名
	signature, err := rsa.SignPSS(cryptorand.Reader, rsaPrivateKey, crypto.SHA256, hash[:], nil)
	if err != nil {
		return "", fmt.Errorf("failed to sign: %w", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}
