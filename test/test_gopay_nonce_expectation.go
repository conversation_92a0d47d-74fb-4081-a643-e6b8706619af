package main

import (
	"encoding/base64"
	"fmt"
)

func main() {
	fmt.Println("=== 分析gopay库对nonce的期望 ===")

	// 真实的微信nonce
	realNonce := "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ"
	fmt.Printf("微信发送的nonce: %s\n", realNonce)
	fmt.Printf("字符串长度: %d\n", len(realNonce))

	// gopay库的处理方式：[]byte(nonce)
	gopayBytes := []byte(realNonce)
	fmt.Printf("gopay转换后长度: %d字节\n", len(gopayBytes))
	fmt.Printf("gopay转换后数据: %x\n", gopayBytes)

	// 分析问题
	fmt.Printf("\n--- 问题分析 ---\n")
	fmt.Printf("AES-GCM要求nonce长度: 12字节\n")
	fmt.Printf("gopay实际传递长度: %d字节\n", len(gopayBytes))
	fmt.Printf("这就是为什么会出现 'incorrect nonce length given to GCM' 错误\n")

	// 正确的处理方式应该是什么？
	fmt.Printf("\n--- 正确的处理方式 ---\n")
	
	// 方式1: base64解码
	decoded, err := base64.StdEncoding.DecodeString(realNonce)
	if err != nil {
		fmt.Printf("❌ Base64解码失败: %v\n", err)
		return
	}
	fmt.Printf("base64解码后长度: %d字节\n", len(decoded))
	fmt.Printf("base64解码后数据: %x\n", decoded)

	// 如果解码后是24字节，需要截取到12字节
	if len(decoded) == 24 {
		fmt.Printf("\n--- 截取策略 ---\n")
		
		// 前12字节
		first12 := decoded[:12]
		fmt.Printf("前12字节: %x\n", first12)
		
		// 后12字节
		last12 := decoded[12:]
		fmt.Printf("后12字节: %x\n", last12)
		
		// 中间12字节
		if len(decoded) >= 18 {
			middle12 := decoded[6:18]
			fmt.Printf("中间12字节: %x\n", middle12)
		}
	}

	// 关键问题：gopay库是否应该先解码nonce？
	fmt.Printf("\n--- 关键问题 ---\n")
	fmt.Printf("gopay库直接使用 []byte(nonce)，这意味着：\n")
	fmt.Printf("1. 要么gopay库的实现有问题\n")
	fmt.Printf("2. 要么微信发送的nonce格式不是我们想的那样\n")
	fmt.Printf("3. 要么需要在调用gopay之前预处理nonce\n")

	// 测试不同的nonce格式
	fmt.Printf("\n--- 测试不同nonce格式 ---\n")
	
	// 如果我们传递解码后的前12字节作为字符串
	if len(decoded) >= 12 {
		nonce12Bytes := decoded[:12]
		nonce12String := string(nonce12Bytes)
		nonce12StringBytes := []byte(nonce12String)
		
		fmt.Printf("前12字节作为字符串: %q\n", nonce12String)
		fmt.Printf("前12字节字符串长度: %d\n", len(nonce12String))
		fmt.Printf("前12字节转换为[]byte长度: %d\n", len(nonce12StringBytes))
		
		if len(nonce12StringBytes) == 12 {
			fmt.Printf("✅ 这种方式可以得到12字节\n")
		} else {
			fmt.Printf("❌ 这种方式得到%d字节\n", len(nonce12StringBytes))
		}
	}

	// 检查是否包含不可打印字符
	fmt.Printf("\n--- 字符分析 ---\n")
	if len(decoded) >= 12 {
		nonce12 := decoded[:12]
		hasUnprintable := false
		for i, b := range nonce12 {
			if b < 32 || b > 126 {
				hasUnprintable = true
				fmt.Printf("位置%d: 0x%02x (不可打印)\n", i, b)
			}
		}
		if !hasUnprintable {
			fmt.Printf("✅ 前12字节都是可打印字符\n")
		} else {
			fmt.Printf("⚠️ 前12字节包含不可打印字符，可能导致字符串处理问题\n")
		}
	}
}
