package main

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	cryptorand "crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 测试修复后的微信支付回调处理

// WechatV3NotifyRequest 微信支付V3回调请求结构
type WechatV3NotifyRequest struct {
	ID           string                 `json:"id"`
	CreateTime   string                 `json:"create_time"`
	EventType    string                 `json:"event_type"`
	ResourceType string                 `json:"resource_type"`
	Resource     WechatV3NotifyResource `json:"resource"`
	Summary      string                 `json:"summary"`
}

// WechatV3NotifyResource 微信支付V3回调资源结构
type WechatV3NotifyResource struct {
	Algorithm      string `json:"algorithm"`
	Ciphertext     string `json:"ciphertext"`
	AssociatedData string `json:"associated_data"`
	Nonce          string `json:"nonce"`
}

// WechatPaymentNotifyData 微信支付成功回调解密后的数据
type WechatPaymentNotifyData struct {
	AppID          string `json:"appid"`
	MchID          string `json:"mchid"`
	OutTradeNo     string `json:"out_trade_no"`
	TransactionID  string `json:"transaction_id"`
	TradeType      string `json:"trade_type"`
	TradeState     string `json:"trade_state"`
	TradeStateDesc string `json:"trade_state_desc"`
	BankType       string `json:"bank_type"`
	Attach         string `json:"attach"`
	SuccessTime    string `json:"success_time"`
	Payer          struct {
		OpenID string `json:"openid"`
	} `json:"payer"`
	Amount struct {
		Total         int    `json:"total"`
		PayerTotal    int    `json:"payer_total"`
		Currency      string `json:"currency"`
		PayerCurrency string `json:"payer_currency"`
	} `json:"amount"`
}

func main() {
	fmt.Println("=== 测试修复后的微信支付回调处理 ===")

	// 配置信息
	config := map[string]string{
		"app_id":     "wxcf9a42f63c23a637",
		"mch_id":     "**********",
		"apiv3_key":  "ZxQr5VpJ9sW4nGfYhLdK8cT3bRm7eNg2",
		"notify_url": "https://6b51df8a6941.ngrok-free.app/notify/wechat",
	}

	// 测试订单号
	orderNo := fmt.Sprintf("TEST_FIX_%d", time.Now().Unix())

	fmt.Printf("测试订单号: %s\n", orderNo)
	fmt.Printf("回调地址: %s\n", config["notify_url"])

	// 发送测试回调
	err := sendTestNotify(config, orderNo)
	if err != nil {
		fmt.Printf("❌ 测试失败: %v\n", err)
		return
	}

	fmt.Println("✅ 测试回调发送成功")
}

func sendTestNotify(config map[string]string, orderNo string) error {
	// 构造支付成功回调数据
	paymentData := WechatPaymentNotifyData{
		AppID:          config["app_id"],
		MchID:          config["mch_id"],
		OutTradeNo:     orderNo,
		TransactionID:  fmt.Sprintf("wx_%d", time.Now().Unix()),
		TradeType:      "NATIVE",
		TradeState:     "SUCCESS",
		TradeStateDesc: "支付成功",
		BankType:       "CMC",
		Attach:         "",
		SuccessTime:    time.Now().Format("2006-01-02T15:04:05+08:00"),
		Payer: struct {
			OpenID string `json:"openid"`
		}{
			OpenID: "test_openid_123",
		},
		Amount: struct {
			Total         int    `json:"total"`
			PayerTotal    int    `json:"payer_total"`
			Currency      string `json:"currency"`
			PayerCurrency string `json:"payer_currency"`
		}{
			Total:         10000, // 100.00元，单位分
			PayerTotal:    10000,
			Currency:      "CNY",
			PayerCurrency: "CNY",
		},
	}

	// 加密数据
	resource, err := encryptNotifyData(paymentData, config["apiv3_key"])
	if err != nil {
		return fmt.Errorf("加密数据失败: %w", err)
	}

	// 构造回调请求
	notifyReq := WechatV3NotifyRequest{
		ID:           fmt.Sprintf("notify_%d", time.Now().Unix()),
		CreateTime:   time.Now().Format("2006-01-02T15:04:05+08:00"),
		EventType:    "TRANSACTION.SUCCESS",
		ResourceType: "encrypt-resource",
		Resource:     *resource,
		Summary:      "支付成功",
	}

	// 序列化请求
	jsonData, err := json.Marshal(notifyReq)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %w", err)
	}

	fmt.Printf("📤 发送回调数据:\n")
	fmt.Printf("  - 订单号: %s\n", paymentData.OutTradeNo)
	fmt.Printf("  - 交易号: %s\n", paymentData.TransactionID)
	fmt.Printf("  - 金额: %d分\n", paymentData.Amount.Total)
	fmt.Printf("  - Nonce长度: %d\n", len(resource.Nonce))

	// 发送HTTP请求
	resp, err := http.Post(config["notify_url"], "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	fmt.Printf("📥 响应状态: %d\n", resp.StatusCode)
	fmt.Printf("📥 响应内容: %s\n", string(respBody))

	if resp.StatusCode == 200 {
		fmt.Println("✅ 回调处理成功")
	} else {
		fmt.Printf("⚠️ 回调处理异常，状态码: %d\n", resp.StatusCode)
	}

	return nil
}

// encryptNotifyData 使用AES-256-GCM加密回调数据
func encryptNotifyData(data interface{}, apiKey string) (*WechatV3NotifyResource, error) {
	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("序列化数据失败: %w", err)
	}

	// 生成随机nonce (12字节)
	nonce := make([]byte, 12)
	if _, err := io.ReadFull(cryptorand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("生成nonce失败: %w", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(apiKey))
	if err != nil {
		return nil, fmt.Errorf("创建AES cipher失败: %w", err)
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %w", err)
	}

	// 设置associated_data
	associatedData := "transaction"

	// 加密数据
	ciphertext := gcm.Seal(nil, nonce, jsonData, []byte(associatedData))

	return &WechatV3NotifyResource{
		Algorithm:      "AEAD_AES_256_GCM",
		Ciphertext:     base64.StdEncoding.EncodeToString(ciphertext),
		AssociatedData: associatedData,
		Nonce:          base64.StdEncoding.EncodeToString(nonce),
	}, nil
}
