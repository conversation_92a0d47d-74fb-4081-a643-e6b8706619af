package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"pay-core/pkg/lock"
	"pay-core/pkg/queue"

	"github.com/redis/go-redis/v9"
)

func main() {
	fmt.Println("=== 分布式锁和延时队列功能测试 ===")

	// 初始化Redis（使用默认配置）
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	// 测试Redis连接
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	fmt.Println("✅ Redis连接成功")

	// 运行测试
	testDistributedLock(ctx, rdb)
	testDelayQueue(ctx, rdb)
	testRefundQueue(ctx, rdb)

	fmt.Println("\n=== 所有测试完成 ===")
}

func testDistributedLock(ctx context.Context, rdb *redis.Client) {
	fmt.Println("\n--- 测试分布式锁 ---")

	orderNo := "TEST_LOCK_001"
	lockKey := lock.PaymentLockKey(orderNo)

	// 测试1: 获取锁
	lock1 := lock.NewRedisLock(rdb, lockKey, 10*time.Second)
	acquired1, err := lock1.TryLock(ctx)
	if err != nil {
		log.Printf("❌ 获取锁失败: %v", err)
		return
	}

	if !acquired1 {
		log.Printf("❌ 第一次获取锁失败")
		return
	}
	fmt.Printf("✅ 第一次获取锁成功: %s\n", lockKey)

	// 测试2: 重复获取锁（应该失败）
	lock2 := lock.NewRedisLock(rdb, lockKey, 10*time.Second)
	acquired2, err := lock2.TryLock(ctx)
	if err != nil {
		log.Printf("❌ 第二次获取锁出错: %v", err)
		return
	}

	if acquired2 {
		log.Printf("❌ 第二次获取锁成功（应该失败）")
		return
	}
	fmt.Printf("✅ 第二次获取锁失败（符合预期）\n")

	// 测试3: 释放锁
	if err := lock1.Unlock(ctx); err != nil {
		log.Printf("❌ 释放锁失败: %v", err)
		return
	}
	fmt.Printf("✅ 锁释放成功\n")

	// 测试4: 再次获取锁（应该成功）
	acquired3, err := lock2.TryLock(ctx)
	if err != nil {
		log.Printf("❌ 第三次获取锁出错: %v", err)
		return
	}

	if !acquired3 {
		log.Printf("❌ 第三次获取锁失败")
		return
	}
	fmt.Printf("✅ 第三次获取锁成功（锁已释放）\n")

	// 清理
	lock2.Unlock(ctx)

	// 测试5: WithLock函数
	fmt.Printf("🔄 测试WithLock函数...\n")
	err = lock.WithLock(ctx, rdb, lockKey, 5*time.Second, func() error {
		fmt.Printf("  📝 在锁保护下执行业务逻辑\n")
		time.Sleep(1 * time.Second)
		return nil
	})
	if err != nil {
		log.Printf("❌ WithLock执行失败: %v", err)
		return
	}
	fmt.Printf("✅ WithLock执行成功\n")
}

func testDelayQueue(ctx context.Context, rdb *redis.Client) {
	fmt.Println("\n--- 测试延时队列 ---")

	delayQueue := queue.NewDelayQueue(rdb, "test_queue")

	// 测试1: 添加订单过期任务
	orderNo := "TEST_ORDER_001"
	expireTime := time.Now().Add(3 * time.Second)
	expireTask := queue.CreateOrderExpireTask(orderNo, expireTime)

	err := delayQueue.AddTask(ctx, expireTask)
	if err != nil {
		log.Printf("❌ 添加订单过期任务失败: %v", err)
		return
	}
	fmt.Printf("✅ 添加订单过期任务成功: %s\n", expireTask.ID)

	// 测试2: 添加订单轮询任务
	pollTime := time.Now().Add(2 * time.Second)
	pollTask := queue.CreateOrderPollTask(orderNo, pollTime, 1)

	err = delayQueue.AddTask(ctx, pollTask)
	if err != nil {
		log.Printf("❌ 添加订单轮询任务失败: %v", err)
		return
	}
	fmt.Printf("✅ 添加订单轮询任务成功: %s\n", pollTask.ID)

	// 测试3: 等待任务到期
	fmt.Printf("⏳ 等待任务到期（3秒）...\n")
	time.Sleep(4 * time.Second)

	// 测试4: 获取到期的轮询任务
	readyPollTasks, err := delayQueue.GetReadyTasks(ctx, queue.TaskTypeOrderPoll, 10)
	if err != nil {
		log.Printf("❌ 获取到期轮询任务失败: %v", err)
		return
	}

	if len(readyPollTasks) > 0 {
		fmt.Printf("✅ 获取到期轮询任务成功，数量: %d\n", len(readyPollTasks))
		for _, task := range readyPollTasks {
			fmt.Printf("  - 任务ID: %s, 类型: %s\n", task.ID, task.Type)
		}
	} else {
		fmt.Printf("⚠️ 没有获取到到期轮询任务\n")
	}

	// 测试5: 获取到期的过期任务
	readyExpireTasks, err := delayQueue.GetReadyTasks(ctx, queue.TaskTypeOrderExpire, 10)
	if err != nil {
		log.Printf("❌ 获取到期过期任务失败: %v", err)
		return
	}

	if len(readyExpireTasks) > 0 {
		fmt.Printf("✅ 获取到期过期任务成功，数量: %d\n", len(readyExpireTasks))
		for _, task := range readyExpireTasks {
			fmt.Printf("  - 任务ID: %s, 类型: %s\n", task.ID, task.Type)
		}
	} else {
		fmt.Printf("⚠️ 没有获取到到期过期任务\n")
	}
}

func testRefundQueue(ctx context.Context, rdb *redis.Client) {
	fmt.Println("\n--- 测试退款延时队列 ---")

	delayQueue := queue.NewDelayQueue(rdb, "test_refund_queue")

	// 测试1: 添加退款过期任务
	refundNo := "TEST_REFUND_001"
	expireTime := time.Now().Add(3 * time.Second)
	expireTask := queue.CreateRefundExpireTask(refundNo, expireTime)

	err := delayQueue.AddTask(ctx, expireTask)
	if err != nil {
		log.Printf("❌ 添加退款过期任务失败: %v", err)
		return
	}
	fmt.Printf("✅ 添加退款过期任务成功: %s\n", expireTask.ID)

	// 测试2: 添加退款轮询任务
	pollTime := time.Now().Add(2 * time.Second)
	pollTask := queue.CreateRefundPollTask(refundNo, pollTime, 1)

	err = delayQueue.AddTask(ctx, pollTask)
	if err != nil {
		log.Printf("❌ 添加退款轮询任务失败: %v", err)
		return
	}
	fmt.Printf("✅ 添加退款轮询任务成功: %s\n", pollTask.ID)

	// 测试3: 等待任务到期
	fmt.Printf("⏳ 等待退款任务到期（3秒）...\n")
	time.Sleep(4 * time.Second)

	// 测试4: 获取到期的退款轮询任务
	readyPollTasks, err := delayQueue.GetReadyTasks(ctx, queue.TaskTypeRefundPoll, 10)
	if err != nil {
		log.Printf("❌ 获取到期退款轮询任务失败: %v", err)
		return
	}

	if len(readyPollTasks) > 0 {
		fmt.Printf("✅ 获取到期退款轮询任务成功，数量: %d\n", len(readyPollTasks))
		for _, task := range readyPollTasks {
			fmt.Printf("  - 任务ID: %s, 类型: %s\n", task.ID, task.Type)
		}
	} else {
		fmt.Printf("⚠️ 没有获取到到期退款轮询任务\n")
	}

	// 测试5: 获取到期的退款过期任务
	readyExpireTasks, err := delayQueue.GetReadyTasks(ctx, queue.TaskTypeRefundExpire, 10)
	if err != nil {
		log.Printf("❌ 获取到期退款过期任务失败: %v", err)
		return
	}

	if len(readyExpireTasks) > 0 {
		fmt.Printf("✅ 获取到期退款过期任务成功，数量: %d\n", len(readyExpireTasks))
		for _, task := range readyExpireTasks {
			fmt.Printf("  - 任务ID: %s, 类型: %s\n", task.ID, task.Type)
		}
	} else {
		fmt.Printf("⚠️ 没有获取到到期退款过期任务\n")
	}

	fmt.Printf("✅ 退款延时队列测试完成\n")
}
