package main

import (
	"context"
	"fmt"
	"time"

	"pay-core/internal/model"
	"pay-core/internal/service"
	"pay-core/pkg/lock"
	"pay-core/pkg/queue"
	"pay-core/pkg/types"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
)

func main() {
	fmt.Println("=== 编译和接口验证测试 ===")

	// 测试1: 验证分布式锁接口
	testLockInterface()

	// 测试2: 验证队列服务接口
	testQueueServiceInterface()

	// 测试3: 验证支付服务接口
	testPaymentServiceInterface()

	// 测试4: 验证模型结构
	testModelStructures()

	fmt.Println("\n=== 所有编译测试完成 ===")
}

func testLockInterface() {
	fmt.Println("\n--- 测试分布式锁接口 ---")

	// 验证锁接口可以正常创建
	var rdb *redis.Client // 模拟Redis客户端
	lockKey := "test_lock"
	expiry := 30 * time.Second

	// 验证NewRedisLock函数存在且可调用
	lock := lock.NewRedisLock(rdb, lockKey, expiry)
	if lock != nil {
		fmt.Printf("✅ NewRedisLock函数可正常调用\n")
	}

	// 验证锁键生成函数
	paymentKey := lock.PaymentLockKey("test_order")
	refundKey := lock.RefundLockKey("test_refund")

	if paymentKey != "" && refundKey != "" {
		fmt.Printf("✅ 锁键生成函数可正常调用\n")
	}

	fmt.Printf("✅ 分布式锁接口验证通过\n")
}

func testQueueServiceInterface() {
	fmt.Println("\n--- 测试队列服务接口 ---")

	// 验证队列任务创建函数
	orderNo := "test_order"
	refundNo := "test_refund"
	testTime := time.Now().Add(5 * time.Minute)

	// 验证订单任务创建
	orderExpireTask := queue.CreateOrderExpireTask(orderNo, testTime)
	orderPollTask := queue.CreateOrderPollTask(orderNo, testTime, 1)

	if orderExpireTask != nil && orderPollTask != nil {
		fmt.Printf("✅ 订单队列任务创建函数可正常调用\n")
	}

	// 验证退款任务创建
	refundExpireTask := queue.CreateRefundExpireTask(refundNo, testTime)
	refundPollTask := queue.CreateRefundPollTask(refundNo, testTime, 1)

	if refundExpireTask != nil && refundPollTask != nil {
		fmt.Printf("✅ 退款队列任务创建函数可正常调用\n")
	}

	// 验证任务类型常量
	taskTypes := []string{
		queue.TaskTypeOrderExpire,
		queue.TaskTypeOrderPoll,
		queue.TaskTypeRefundExpire,
		queue.TaskTypeRefundPoll,
	}

	if len(taskTypes) == 4 {
		fmt.Printf("✅ 任务类型常量定义完整\n")
	}

	fmt.Printf("✅ 队列服务接口验证通过\n")
}

func testPaymentServiceInterface() {
	fmt.Println("\n--- 测试支付服务接口 ---")

	// 验证PaymentService接口包含所有必要方法
	// 这里我们通过类型断言来验证接口方法的存在

	// 创建一个模拟的PaymentService实现来验证接口
	type mockPaymentService struct{}

	// 验证接口方法签名（编译时检查）
	var _ service.PaymentService = (*mockPaymentService)(nil)

	fmt.Printf("✅ PaymentService接口方法签名验证通过\n")

	// 验证新增的方法存在
	ctx := context.Background()
	orderNo := "test_order"
	refundNo := "test_refund"
	pollCount := 1

	// 这些调用会在编译时验证方法签名是否正确
	var ps service.PaymentService
	if ps != nil {
		// 注意：这些调用不会实际执行，只是编译时验证
		_ = ps.CloseExpiredRefund(ctx, refundNo)
		_ = ps.PollRefundStatus(ctx, refundNo, pollCount)
		_ = ps.PollOrderStatus(ctx, orderNo, pollCount)
	}

	fmt.Printf("✅ 新增的支付服务方法签名验证通过\n")
}

// 实现mockPaymentService的所有方法以验证接口完整性
func (m *mockPaymentService) CreateOrder(ctx context.Context, appID uint64, req *model.CreateOrderRequest) (*model.CreateOrderResponse, error) {
	return nil, nil
}

func (m *mockPaymentService) GetOrder(ctx context.Context, appID uint64, orderNo string) (*model.PaymentOrder, error) {
	return nil, nil
}

func (m *mockPaymentService) QueryOrder(ctx context.Context, appID uint64, req *model.QueryOrderRequest) (*model.PaymentOrder, error) {
	return nil, nil
}

func (m *mockPaymentService) HandleNotify(ctx context.Context, channel string, req interface{}) error {
	return nil
}

func (m *mockPaymentService) RefundOrder(ctx context.Context, appID uint64, orderNo string, req *model.RefundOrderRequest) (*model.RefundOrder, error) {
	return nil, nil
}

func (m *mockPaymentService) CloseExpiredOrders(ctx context.Context) error {
	return nil
}

func (m *mockPaymentService) CloseExpiredOrder(ctx context.Context, orderNo string) error {
	return nil
}

func (m *mockPaymentService) PollOrderStatus(ctx context.Context, orderNo string, pollCount int) error {
	return nil
}

func (m *mockPaymentService) CloseExpiredRefund(ctx context.Context, refundNo string) error {
	return nil
}

func (m *mockPaymentService) PollRefundStatus(ctx context.Context, refundNo string, pollCount int) error {
	return nil
}

func testModelStructures() {
	fmt.Println("\n--- 测试模型结构 ---")

	// 验证PaymentOrder模型
	order := &model.PaymentOrder{
		OrderNo:     "test_order",
		AppID:       1,
		AppOrderNo:  "app_order_001",
		TotalAmount: decimal.NewFromFloat(100.00),
		Subject:     "测试订单",
		PayChannel:  "wechat",
		Status:      model.OrderStatusWaitingPay,
		ExpireTime:  types.Time{Time: time.Now().Add(30 * time.Minute)},
	}

	if order != nil {
		fmt.Printf("✅ PaymentOrder模型结构验证通过\n")
	}

	// 验证RefundOrder模型
	refund := &model.RefundOrder{
		RefundNo:     "test_refund",
		OrderNo:      "test_order",
		AppID:        1,
		AppRefundNo:  "app_refund_001",
		RefundAmount: decimal.NewFromFloat(50.00),
		RefundReason: "测试退款",
		Status:       model.RefundStatusProcessing,
	}

	if refund != nil {
		fmt.Printf("✅ RefundOrder模型结构验证通过\n")
	}

	// 验证PaymentRecord模型
	record := &model.PaymentRecord{
		OrderNo:       "test_order",
		OutTradeNo:    "test_order",
		TradeNo:       "wx_test_123",
		PayChannel:    "wechat",
		TotalAmount:   decimal.NewFromFloat(100.00),
		ReceiptAmount: decimal.NewFromFloat(100.00),
		TradeTime:     types.Now(),
	}

	if record != nil {
		fmt.Printf("✅ PaymentRecord模型结构验证通过\n")
	}

	fmt.Printf("✅ 模型结构验证通过\n")
}
