package main

import (
	"crypto/aes"
	"crypto/cipher"
	"fmt"
)

func main() {
	fmt.Println("=== 测试AES-GCM对nonce长度的要求 ===")

	// 测试key（32字节）
	key := []byte("ZxQr5VpJ9sW4nGfYhLdK8cT3bRm7eNg2")
	fmt.Printf("Key长度: %d字节\n", len(key))

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		fmt.Printf("创建AES cipher失败: %v\n", err)
		return
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		fmt.Printf("创建GCM失败: %v\n", err)
		return
	}

	fmt.Printf("GCM NonceSize: %d字节\n", gcm.NonceSize())
	fmt.Printf("GCM Overhead: %d字节\n", gcm.Overhead())

	// 测试不同长度的nonce
	testNonceLengths := []int{12, 16, 24, 32}

	for _, length := range testNonceLengths {
		fmt.Printf("\n--- 测试%d字节nonce ---\n", length)

		nonce := make([]byte, length)
		for i := range nonce {
			nonce[i] = byte(i % 256)
		}

		plaintext := []byte("test message")
		additional := []byte("transaction")

		// 尝试加密
		func() {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("❌ %d字节nonce加密panic: %v\n", length, r)
				}
			}()

			ciphertext := gcm.Seal(nil, nonce, plaintext, additional)
			if ciphertext != nil {
				fmt.Printf("✅ %d字节nonce加密成功\n", length)
			}
		}()
	}

	// 测试真实的微信nonce
	fmt.Printf("\n--- 测试真实微信nonce ---\n")

	// 方式1: 直接使用字符串（32字节）
	realNonceStr := "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ"
	realNonceBytes := []byte(realNonceStr)
	fmt.Printf("真实nonce字符串长度: %d字节\n", len(realNonceBytes))

	plaintext := []byte("test message")
	additional := []byte("transaction")

	func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("❌ 真实nonce(字符串)加密panic: %v\n", r)
			}
		}()

		ciphertext := gcm.Seal(nil, realNonceBytes, plaintext, additional)
		if ciphertext != nil {
			fmt.Printf("✅ 真实nonce(字符串)加密成功\n")
		}
	}()
}
