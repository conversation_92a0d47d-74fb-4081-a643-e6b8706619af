package main

import (
	"encoding/base64"
	"fmt"
)

// 测试新的nonce填充修复逻辑

func main() {
	fmt.Println("=== 测试Nonce填充修复逻辑 ===")

	// 测试不同长度的nonce
	testCases := []struct {
		name  string
		nonce string
		desc  string
	}{
		{
			name:  "9字节nonce（当前问题）",
			nonce: "ybWU0ak09bcX",
			desc:  "解码后9字节，需要填充到12字节",
		},
		{
			name:  "12字节nonce",
			nonce: "arRu9htISJMP",
			desc:  "解码后12字节，直接使用",
		},
		{
			name:  "24字节nonce",
			nonce: "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ",
			desc:  "解码后24字节，截取前12字节",
		},
		{
			name:  "16字节nonce",
			nonce: "dGVzdDE2Ynl0ZXNub25jZQ==", // "test16bytesnonce" base64编码
			desc:  "解码后16字节，截取前12字节",
		},
	}

	for _, tc := range testCases {
		fmt.Printf("\n--- %s ---\n", tc.name)
		fmt.Printf("描述: %s\n", tc.desc)
		
		processedNonce := processNonce(tc.nonce)
		processedBytes := []byte(processedNonce)
		
		fmt.Printf("原始nonce: %s (长度: %d)\n", tc.nonce, len(tc.nonce))
		fmt.Printf("处理后nonce长度: %d字节\n", len(processedBytes))
		fmt.Printf("处理后nonce(hex): %x\n", processedBytes)
		
		if len(processedBytes) == 12 {
			fmt.Println("✅ 长度正确 (12字节)")
		} else {
			fmt.Printf("❌ 长度错误: %d字节，期望12字节\n", len(processedBytes))
		}
	}

	// 特别测试当前问题的nonce
	fmt.Printf("\n=== 特别测试当前问题的nonce ===\n")
	problemNonce := "ybWU0ak09bcX"
	fmt.Printf("问题nonce: %s\n", problemNonce)
	
	// 解码分析
	decoded, err := base64.StdEncoding.DecodeString(problemNonce)
	if err != nil {
		fmt.Printf("❌ 解码失败: %v\n", err)
		return
	}
	
	fmt.Printf("解码后长度: %d字节\n", len(decoded))
	fmt.Printf("解码后数据(hex): %x\n", decoded)
	
	// 应用修复逻辑
	processed := processNonce(problemNonce)
	processedBytes := []byte(processed)
	
	fmt.Printf("修复后长度: %d字节\n", len(processedBytes))
	fmt.Printf("修复后数据(hex): %x\n", processedBytes)
	
	if len(processedBytes) == 12 {
		fmt.Println("✅ 修复成功！nonce长度现在是12字节")
	} else {
		fmt.Printf("❌ 修复失败，长度仍然是%d字节\n", len(processedBytes))
	}
}

// processNonce 模拟修复后的nonce处理逻辑
func processNonce(nonce string) string {
	// 尝试base64解码
	decoded, err := base64.StdEncoding.DecodeString(nonce)
	if err != nil {
		// 如果解码失败，直接返回原始nonce
		fmt.Printf("⚠️ Base64解码失败: %v，使用原始nonce\n", err)
		return nonce
	}

	fmt.Printf("Base64解码成功，长度: %d字节\n", len(decoded))

	// 如果解码后长度是24字节，截取前12字节
	if len(decoded) == 24 {
		truncated := decoded[:12]
		result := string(truncated)
		fmt.Printf("🔧 24字节nonce截取前12字节\n")
		return result
	}

	// 如果解码后长度是12字节，直接转换为字符串
	if len(decoded) == 12 {
		result := string(decoded)
		fmt.Printf("✅ 12字节nonce直接转换为字符串\n")
		return result
	}

	// 如果解码后长度小于12字节，填充到12字节
	if len(decoded) < 12 {
		// 创建12字节的缓冲区
		padded := make([]byte, 12)
		// 复制原始数据到缓冲区开头
		copy(padded, decoded)
		// 剩余部分用零填充（已经是零值）
		result := string(padded)
		fmt.Printf("🔧 %d字节nonce填充到12字节\n", len(decoded))
		return result
	}

	// 如果解码后长度大于12字节但不是24字节，截取前12字节
	if len(decoded) > 12 {
		truncated := decoded[:12]
		result := string(truncated)
		fmt.Printf("🔧 %d字节nonce截取前12字节\n", len(decoded))
		return result
	}

	// 理论上不会到达这里
	fmt.Printf("⚠️ 意外的nonce处理情况，使用原始nonce\n")
	return nonce
}
