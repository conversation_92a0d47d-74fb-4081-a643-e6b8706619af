package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// 测试修复后的nonce处理 - 使用真实的微信回调数据

func main() {
	fmt.Println("=== 测试修复后的Nonce处理 ===")

	// 使用真实的微信支付回调数据结构
	// 这里的nonce "ybWU0ak09bcX" 是从实际错误日志中提取的
	realCallbackData := map[string]interface{}{
		"id":            "EV-2018022511223320873",
		"create_time":   "2025-08-24T11:29:22+08:00",
		"resource_type": "encrypt-resource",
		"event_type":    "TRANSACTION.SUCCESS",
		"summary":       "支付成功",
		"resource": map[string]interface{}{
			"original_type":   "transaction",
			"algorithm":       "AEAD_AES_256_GCM",
			"ciphertext":      "simulated_encrypted_data_for_testing_purposes_only",
			"associated_data": "transaction",
			"nonce":           "ybWU0ak09bcX", // 真实的9字节nonce（base64解码后）
		},
	}

	// 分析这个nonce
	analyzeNonce("ybWU0ak09bcX")

	// 发送测试请求
	err := sendTestCallback(realCallbackData)
	if err != nil {
		fmt.Printf("❌ 测试失败: %v\n", err)
		return
	}

	fmt.Println("✅ 测试完成")
}

func analyzeNonce(nonce string) {
	fmt.Printf("\n--- Nonce分析 ---\n")
	fmt.Printf("测试nonce: %s\n", nonce)
	fmt.Printf("字符串长度: %d\n", len(nonce))

	// 模拟我们的修复逻辑
	fmt.Printf("\n--- 修复逻辑验证 ---\n")
	fmt.Printf("1. 接收到base64编码的nonce: %s\n", nonce)
	
	// 解码
	decoded, err := base64.StdEncoding.DecodeString(nonce)
	if err != nil {
		fmt.Printf("❌ 解码失败: %v\n", err)
		return
	}
	
	fmt.Printf("2. base64解码后长度: %d字节\n", len(decoded))
	fmt.Printf("3. 解码数据(hex): %x\n", decoded)
	fmt.Printf("4. AES-GCM要求: 12字节\n")
	
	if len(decoded) < 12 {
		fmt.Printf("5. 检测到短nonce，需要填充到12字节\n")
		padded := make([]byte, 12)
		copy(padded, decoded)
		fmt.Printf("6. 填充后数据(hex): %x\n", padded)
		fmt.Printf("✅ 修复逻辑：%d字节 → 12字节填充\n", len(decoded))
	} else {
		fmt.Printf("5. nonce长度足够，无需填充\n")
	}
}

func sendTestCallback(callbackData map[string]interface{}) error {
	fmt.Printf("\n--- 发送测试回调 ---\n")

	// 序列化数据
	jsonData, err := json.Marshal(callbackData)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %w", err)
	}

	// 目标URL
	url := "http://localhost:8080/notify/wechat"

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/4.0")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Encoding", "gzip")

	// 设置微信支付特有的头部（使用真实的头部数据）
	req.Header.Set("Wechatpay-Nonce", "nQcS2j7Y4xDfnenvNSCfEgPTX1ZQL4FO")
	req.Header.Set("Wechatpay-Serial", "PUB_KEY_ID_0112640559012025082200382286000401")
	req.Header.Set("Wechatpay-Signature", "lXXprel4JLGsQR0UVzI0FrLbWn6VTxFvGmXfbnf2awfkTRxPCaFNRujwUWSIqrDl2HyaMXDtaAiRwTOISg/jdhoJ/RteB2ITaxsfnn1dCiLFyuNjHnc+ftHIcdfEOdiadru0+kJwU51J7ISx/J+DuUkOAF5MT83fPsJeQYmrjkfsl3luFYQmJxIboy2o8GocGq21WzkKxfSj/L7ZmLt4puP5CAXREeEB9AgDAuYUjgb+EU0Hj4vqENQ7t+hm/FfyVDRo9YFfPlQgHXcSaUKYfbqIn1U87eQwZvXVgZ6q1eBLtm93gqijIdxle6biEsWQgcREhP6u3h359RgnVB96qQ==")
	req.Header.Set("Wechatpay-Signature-Type", "WECHATPAY2-SHA256-RSA2048")
	req.Header.Set("Wechatpay-Timestamp", "1756006163")

	fmt.Printf("📤 请求信息:\n")
	fmt.Printf("  URL: %s\n", url)
	fmt.Printf("  Method: POST\n")
	fmt.Printf("  Content-Type: %s\n", req.Header.Get("Content-Type"))
	fmt.Printf("  Wechatpay-Nonce: %s\n", req.Header.Get("Wechatpay-Nonce"))
	fmt.Printf("  Body nonce: %s\n", callbackData["resource"].(map[string]interface{})["nonce"])

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	fmt.Printf("\n📥 响应结果:\n")
	fmt.Printf("  状态码: %d\n", resp.StatusCode)
	fmt.Printf("  响应内容: %s\n", string(respBody))

	// 分析结果
	fmt.Printf("\n🔍 结果分析:\n")
	switch resp.StatusCode {
	case 200:
		fmt.Println("  ✅ 回调处理成功 - nonce问题已解决！")
	case 500:
		respStr := string(respBody)
		if strings.Contains(respStr, "nonce") || strings.Contains(respStr, "GCM") {
			fmt.Println("  ❌ 仍然是nonce相关问题")
		} else {
			fmt.Println("  ⚠️ 其他500错误（可能是业务逻辑问题，但nonce处理正常）")
		}
	default:
		fmt.Printf("  ⚠️ 其他状态码: %d\n", resp.StatusCode)
	}

	return nil
}
