package main

import (
	"encoding/base64"
	"fmt"
)

// 测试nonce处理逻辑

func main() {
	fmt.Println("=== 测试Nonce处理逻辑 ===")

	// 测试真实的微信nonce数据
	testCases := []struct {
		name  string
		nonce string
	}{
		{
			name:  "支付回调nonce",
			nonce: "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ",
		},
		{
			name:  "退款回调nonce",
			nonce: "xl0OyL12rzsKkdTKUFvJCw1I49BV9vsb",
		},
		{
			name:  "最新退款回调nonce",
			nonce: "arRu9htISJMP", // 从日志中看到的短nonce
		},
	}

	for _, tc := range testCases {
		fmt.Printf("\n--- %s ---\n", tc.name)
		processedNonce := processNonce(tc.nonce)
		fmt.Printf("原始nonce: %s (长度: %d)\n", tc.nonce, len(tc.nonce))
		fmt.Printf("处理后nonce: %q (长度: %d)\n", processedNonce, len(processedNonce))
		
		// 验证处理后的nonce转换为字节数组的长度
		processedBytes := []byte(processedNonce)
		fmt.Printf("转换为[]byte长度: %d\n", len(processedBytes))
		
		if len(processedBytes) == 12 {
			fmt.Println("✅ 处理后长度正确 (12字节)")
		} else {
			fmt.Printf("❌ 处理后长度错误: %d字节，期望12字节\n", len(processedBytes))
		}
	}
}

// processNonce 模拟我们的nonce处理逻辑
func processNonce(nonce string) string {
	// 尝试base64解码
	decoded, err := base64.StdEncoding.DecodeString(nonce)
	if err != nil {
		// 如果解码失败，直接返回原始nonce
		fmt.Printf("⚠️ Base64解码失败: %v，使用原始nonce\n", err)
		return nonce
	}

	fmt.Printf("Base64解码成功，长度: %d字节\n", len(decoded))

	// 如果解码后长度是24字节，截取前12字节
	if len(decoded) == 24 {
		truncated := decoded[:12]
		result := string(truncated)
		fmt.Printf("🔧 24字节nonce截取前12字节\n")
		fmt.Printf("截取的字节: %x\n", truncated)
		return result
	}

	// 如果解码后长度是12字节，直接转换为字符串
	if len(decoded) == 12 {
		result := string(decoded)
		fmt.Printf("✅ 12字节nonce直接转换为字符串\n")
		fmt.Printf("解码的字节: %x\n", decoded)
		return result
	}

	// 其他长度，记录警告并返回原始nonce
	fmt.Printf("⚠️ 意外的nonce长度: %d字节，使用原始nonce\n", len(decoded))
	return nonce
}
