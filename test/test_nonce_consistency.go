package main

import (
	"encoding/base64"
	"fmt"
)

// 验证支付和退款回调使用相同的nonce处理方法

func main() {
	fmt.Println("=== 验证支付和退款回调nonce处理一致性 ===")

	// 真实的微信nonce数据
	testCases := []struct {
		name  string
		nonce string
		type_ string
	}{
		{
			name:  "支付回调nonce",
			nonce: "AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ",
			type_: "PAYMENT",
		},
		{
			name:  "退款回调nonce (长)",
			nonce: "xl0OyL12rzsKkdTKUFvJCw1I49BV9vsb",
			type_: "REFUND",
		},
		{
			name:  "退款回调nonce (短)",
			nonce: "arRu9htISJMP",
			type_: "REFUND",
		},
	}

	fmt.Println("\n--- 使用统一的processNonce方法处理 ---")
	
	for _, tc := range testCases {
		fmt.Printf("\n🔸 %s (%s)\n", tc.name, tc.type_)
		
		// 使用统一的处理方法
		processedNonce := processNonce(tc.nonce)
		processedBytes := []byte(processedNonce)
		
		fmt.Printf("  原始nonce: %s (长度: %d)\n", tc.nonce, len(tc.nonce))
		fmt.Printf("  处理后nonce长度: %d字节\n", len(processedBytes))
		
		if len(processedBytes) == 12 {
			fmt.Printf("  ✅ 长度正确 (12字节)\n")
		} else {
			fmt.Printf("  ❌ 长度错误: %d字节\n", len(processedBytes))
		}
	}

	// 验证处理结果的一致性
	fmt.Println("\n--- 验证处理结果一致性 ---")
	
	// 测试相同长度的nonce是否得到相同的处理结果
	sameLength32Nonces := []string{
		"AZ7olyQyArqJOhL4EPNcqdQAU78ajWIQ", // 支付
		"xl0OyL12rzsKkdTKUFvJCw1I49BV9vsb", // 退款
	}
	
	fmt.Println("32字符nonce处理结果:")
	for i, nonce := range sameLength32Nonces {
		processed := processNonce(nonce)
		fmt.Printf("  Nonce %d: %d字符 → %d字节\n", i+1, len(nonce), len([]byte(processed)))
	}
	
	// 验证所有处理后的nonce都是12字节
	allCorrect := true
	for _, tc := range testCases {
		processed := processNonce(tc.nonce)
		if len([]byte(processed)) != 12 {
			allCorrect = false
			break
		}
	}
	
	if allCorrect {
		fmt.Println("\n✅ 所有nonce处理后都是12字节，处理方法一致")
	} else {
		fmt.Println("\n❌ 存在处理不一致的情况")
	}
}

// processNonce 统一的nonce处理方法（与实际代码相同）
func processNonce(nonce string) string {
	// 尝试base64解码
	decoded, err := base64.StdEncoding.DecodeString(nonce)
	if err != nil {
		// 如果解码失败，直接返回原始nonce
		fmt.Printf("    ⚠️ Base64解码失败，使用原始nonce\n")
		return nonce
	}

	// 如果解码后长度是24字节，截取前12字节
	if len(decoded) == 24 {
		truncated := decoded[:12]
		result := string(truncated)
		fmt.Printf("    🔧 24字节解码 → 截取前12字节\n")
		return result
	}

	// 如果解码后长度是12字节，直接转换为字符串
	if len(decoded) == 12 {
		result := string(decoded)
		fmt.Printf("    ✅ 12字节解码 → 直接转换\n")
		return result
	}

	// 其他长度，记录警告并返回原始nonce
	fmt.Printf("    ⚠️ 意外长度(%d字节)，使用原始nonce\n", len(decoded))
	return nonce
}
