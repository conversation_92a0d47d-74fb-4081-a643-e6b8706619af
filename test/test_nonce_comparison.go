package main

import (
	"encoding/base64"
	"fmt"
)

// 测试两个不同nonce的关系

func main() {
	fmt.Println("=== 测试Nonce比较分析 ===")

	// 从HTTP头部获取的nonce
	headerNonce := "nQcS2j7Y4xDfnenvNSCfEgPTX1ZQL4FO"
	
	// 从JSON body中解析出的nonce
	bodyNonce := "ybWU0ak09bcX"

	fmt.Printf("HTTP头部nonce: %s (长度: %d)\n", headerNonce, len(headerNonce))
	fmt.Printf("JSON body nonce: %s (长度: %d)\n", bodyNonce, len(bodyNonce))

	// 分析HTTP头部nonce
	fmt.Printf("\n--- 分析HTTP头部nonce ---\n")
	headerDecoded, err := base64.StdEncoding.DecodeString(headerNonce)
	if err != nil {
		fmt.Printf("❌ HTTP头部nonce解码失败: %v\n", err)
	} else {
		fmt.Printf("解码成功，长度: %d字节\n", len(headerDecoded))
		fmt.Printf("解码数据(hex): %x\n", headerDecoded)
	}

	// 分析JSON body nonce
	fmt.Printf("\n--- 分析JSON body nonce ---\n")
	bodyDecoded, err := base64.StdEncoding.DecodeString(bodyNonce)
	if err != nil {
		fmt.Printf("❌ JSON body nonce解码失败: %v\n", err)
	} else {
		fmt.Printf("解码成功，长度: %d字节\n", len(bodyDecoded))
		fmt.Printf("解码数据(hex): %x\n", bodyDecoded)
	}

	// 检查是否有截断关系
	fmt.Printf("\n--- 检查截断关系 ---\n")
	if len(headerDecoded) > 0 && len(bodyDecoded) > 0 {
		if len(headerDecoded) > len(bodyDecoded) {
			// 检查body nonce是否是header nonce的前缀
			if len(headerDecoded) >= len(bodyDecoded) {
				headerPrefix := headerDecoded[:len(bodyDecoded)]
				fmt.Printf("HTTP头部前%d字节: %x\n", len(bodyDecoded), headerPrefix)
				
				if fmt.Sprintf("%x", headerPrefix) == fmt.Sprintf("%x", bodyDecoded) {
					fmt.Println("✅ JSON body nonce是HTTP头部nonce的前缀！")
				} else {
					fmt.Println("❌ JSON body nonce不是HTTP头部nonce的前缀")
				}
			}
		}
		
		// 检查是否有其他关系
		fmt.Printf("\n--- 其他可能的关系 ---\n")
		
		// 检查是否是后缀
		if len(headerDecoded) >= len(bodyDecoded) {
			headerSuffix := headerDecoded[len(headerDecoded)-len(bodyDecoded):]
			fmt.Printf("HTTP头部后%d字节: %x\n", len(bodyDecoded), headerSuffix)
			
			if fmt.Sprintf("%x", headerSuffix) == fmt.Sprintf("%x", bodyDecoded) {
				fmt.Println("✅ JSON body nonce是HTTP头部nonce的后缀！")
			}
		}
		
		// 检查是否是中间部分
		if len(headerDecoded) > len(bodyDecoded) {
			for i := 0; i <= len(headerDecoded)-len(bodyDecoded); i++ {
				headerMiddle := headerDecoded[i : i+len(bodyDecoded)]
				if fmt.Sprintf("%x", headerMiddle) == fmt.Sprintf("%x", bodyDecoded) {
					fmt.Printf("✅ JSON body nonce是HTTP头部nonce的中间部分（偏移%d）！\n", i)
					break
				}
			}
		}
	}

	// 应用我们的修复逻辑
	fmt.Printf("\n--- 应用修复逻辑 ---\n")
	
	fmt.Printf("处理HTTP头部nonce:\n")
	processedHeader := processNonce(headerNonce)
	fmt.Printf("结果长度: %d字节\n", len([]byte(processedHeader)))
	
	fmt.Printf("\n处理JSON body nonce:\n")
	processedBody := processNonce(bodyNonce)
	fmt.Printf("结果长度: %d字节\n", len([]byte(processedBody)))
	
	// 检查处理后的结果是否相同
	if fmt.Sprintf("%x", []byte(processedHeader)) == fmt.Sprintf("%x", []byte(processedBody)) {
		fmt.Println("\n✅ 处理后的nonce相同！")
	} else {
		fmt.Println("\n❌ 处理后的nonce不同")
		fmt.Printf("HTTP头部处理后: %x\n", []byte(processedHeader))
		fmt.Printf("JSON body处理后: %x\n", []byte(processedBody))
	}
}

// processNonce 模拟我们的nonce处理逻辑
func processNonce(nonce string) string {
	// 尝试base64解码
	decoded, err := base64.StdEncoding.DecodeString(nonce)
	if err != nil {
		fmt.Printf("⚠️ Base64解码失败: %v，使用原始nonce\n", err)
		return nonce
	}

	fmt.Printf("Base64解码成功，长度: %d字节\n", len(decoded))

	// 如果解码后长度是24字节，截取前12字节
	if len(decoded) == 24 {
		truncated := decoded[:12]
		result := string(truncated)
		fmt.Printf("🔧 24字节nonce截取前12字节\n")
		return result
	}

	// 如果解码后长度是12字节，直接转换为字符串
	if len(decoded) == 12 {
		result := string(decoded)
		fmt.Printf("✅ 12字节nonce直接转换为字符串\n")
		return result
	}

	// 如果解码后长度小于12字节，填充到12字节
	if len(decoded) < 12 {
		padded := make([]byte, 12)
		copy(padded, decoded)
		result := string(padded)
		fmt.Printf("🔧 %d字节nonce填充到12字节\n", len(decoded))
		return result
	}

	// 如果解码后长度大于12字节但不是24字节，截取前12字节
	if len(decoded) > 12 {
		truncated := decoded[:12]
		result := string(truncated)
		fmt.Printf("🔧 %d字节nonce截取前12字节\n", len(decoded))
		return result
	}

	fmt.Printf("⚠️ 意外的nonce处理情况，使用原始nonce\n")
	return nonce
}
