package main

import (
	"encoding/base64"
	"fmt"
)

// 测试 Go 字符串处理 null 字节的行为

func main() {
	fmt.Println("=== 测试字符串中的 Null 字节处理 ===")

	// 模拟我们的 nonce 处理
	nonce := "ybWU0ak09bcX"
	decoded, _ := base64.StdEncoding.DecodeString(nonce)
	
	fmt.Printf("原始 nonce: %s\n", nonce)
	fmt.Printf("解码后长度: %d\n", len(decoded))
	fmt.Printf("解码后数据: %x\n", decoded)

	// 测试零填充
	fmt.Printf("\n--- 测试零填充 ---\n")
	padded := make([]byte, 12)
	copy(padded, decoded)
	
	fmt.Printf("填充后字节数组: %x\n", padded)
	fmt.Printf("字节数组长度: %d\n", len(padded))
	
	// 转换为字符串
	paddedStr := string(padded)
	fmt.Printf("转换为字符串后长度: %d\n", len(paddedStr))
	fmt.Printf("字符串内容(hex): %x\n", []byte(paddedStr))
	
	// 再次转换为字节数组
	backToBytes := []byte(paddedStr)
	fmt.Printf("字符串转回字节数组长度: %d\n", len(backToBytes))
	fmt.Printf("转回的字节数组: %x\n", backToBytes)
	
	// 检查是否相等
	if len(backToBytes) == 12 {
		fmt.Println("✅ 零填充策略正常")
	} else {
		fmt.Printf("❌ 零填充策略有问题：期望12字节，实际%d字节\n", len(backToBytes))
	}

	// 测试重复填充策略
	fmt.Printf("\n--- 测试重复填充策略 ---\n")
	repeated := make([]byte, 12)
	for i := 0; i < 12; i++ {
		repeated[i] = decoded[i%len(decoded)]
	}
	
	fmt.Printf("重复填充字节数组: %x\n", repeated)
	fmt.Printf("字节数组长度: %d\n", len(repeated))
	
	repeatedStr := string(repeated)
	fmt.Printf("转换为字符串后长度: %d\n", len(repeatedStr))
	
	backToBytes2 := []byte(repeatedStr)
	fmt.Printf("字符串转回字节数组长度: %d\n", len(backToBytes2))
	
	if len(backToBytes2) == 12 {
		fmt.Println("✅ 重复填充策略正常")
	} else {
		fmt.Printf("❌ 重复填充策略有问题：期望12字节，实际%d字节\n", len(backToBytes2))
	}

	// 测试固定值填充策略
	fmt.Printf("\n--- 测试固定值填充策略 ---\n")
	fixed := make([]byte, 12)
	copy(fixed, decoded)
	// 用 0xFF 填充剩余部分
	for i := len(decoded); i < 12; i++ {
		fixed[i] = 0xFF
	}
	
	fmt.Printf("固定值填充字节数组: %x\n", fixed)
	fixedStr := string(fixed)
	fmt.Printf("转换为字符串后长度: %d\n", len(fixedStr))
	
	backToBytes3 := []byte(fixedStr)
	fmt.Printf("字符串转回字节数组长度: %d\n", len(backToBytes3))
	
	if len(backToBytes3) == 12 {
		fmt.Println("✅ 固定值填充策略正常")
	} else {
		fmt.Printf("❌ 固定值填充策略有问题：期望12字节，实际%d字节\n", len(backToBytes3))
	}

	// 推荐的解决方案
	fmt.Printf("\n--- 推荐解决方案 ---\n")
	if len(backToBytes) != 12 {
		fmt.Println("问题确认：零填充导致字符串截断")
		fmt.Println("推荐解决方案：使用重复填充或固定非零值填充")
	} else {
		fmt.Println("零填充策略正常，问题可能在其他地方")
	}
}
