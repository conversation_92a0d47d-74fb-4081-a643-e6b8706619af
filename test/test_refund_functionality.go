package main

import (
	"fmt"

	"pay-core/pkg/robot"

	"github.com/shopspring/decimal"
)

func main() {
	testRefundMain()
}

func testRefundMain() {
	fmt.Println("=== 退款功能验证测试 ===")

	// 测试1: 验证退款请求结构体
	testRefundRequestStruct()

	// 测试2: 验证退款响应结构体
	testRefundResponseStruct()

	// 测试3: 验证退款客户端方法存在
	testRefundClientMethods()

	fmt.Println("\n=== 退款功能验证完成 ===")
	fmt.Println("✅ 所有退款相关功能编译通过，代码结构正确")
}

func testRefundRequestStruct() {
	fmt.Println("\n--- 测试退款请求结构体 ---")

	// 创建退款请求
	refundReq := &robot.RefundRequest{
		Amount:          decimal.NewFromFloat(50.25),
		OriginalOrderID: "RECHARGE_1692345678",
		RefundOrderID:   "REFUND_1692345679",
		RefundReason:    "用户申请退款",
	}

	// 验证字段
	if refundReq.Amount.Equal(decimal.NewFromFloat(50.25)) {
		fmt.Printf("✅ 退款金额字段正确: %s\n", refundReq.Amount.String())
	} else {
		fmt.Printf("❌ 退款金额字段错误\n")
	}

	if refundReq.OriginalOrderID == "RECHARGE_1692345678" {
		fmt.Printf("✅ 原始订单ID字段正确: %s\n", refundReq.OriginalOrderID)
	} else {
		fmt.Printf("❌ 原始订单ID字段错误\n")
	}

	if refundReq.RefundOrderID == "REFUND_1692345679" {
		fmt.Printf("✅ 退款订单ID字段正确: %s\n", refundReq.RefundOrderID)
	} else {
		fmt.Printf("❌ 退款订单ID字段错误\n")
	}

	if refundReq.RefundReason == "用户申请退款" {
		fmt.Printf("✅ 退款原因字段正确: %s\n", refundReq.RefundReason)
	} else {
		fmt.Printf("❌ 退款原因字段错误\n")
	}
}

func testRefundResponseStruct() {
	fmt.Println("\n--- 测试退款响应结构体 ---")

	// 创建退款响应
	refundResp := &robot.RefundResponse{
		Code:    200,
		Message: "退款成功",
		Data: struct {
			RefundAmount decimal.Decimal `json:"refund_amount"`
			Balance      decimal.Decimal `json:"balance"`
		}{
			RefundAmount: decimal.NewFromFloat(50.25),
			Balance:      decimal.NewFromFloat(149.75),
		},
	}

	// 验证字段
	if refundResp.Code == 200 {
		fmt.Printf("✅ 响应码字段正确: %d\n", refundResp.Code)
	} else {
		fmt.Printf("❌ 响应码字段错误\n")
	}

	if refundResp.Message == "退款成功" {
		fmt.Printf("✅ 响应消息字段正确: %s\n", refundResp.Message)
	} else {
		fmt.Printf("❌ 响应消息字段错误\n")
	}

	if refundResp.Data.RefundAmount.Equal(decimal.NewFromFloat(50.25)) {
		fmt.Printf("✅ 退款金额字段正确: %s\n", refundResp.Data.RefundAmount.String())
	} else {
		fmt.Printf("❌ 退款金额字段错误\n")
	}

	if refundResp.Data.Balance.Equal(decimal.NewFromFloat(149.75)) {
		fmt.Printf("✅ 余额字段正确: %s\n", refundResp.Data.Balance.String())
	} else {
		fmt.Printf("❌ 余额字段错误\n")
	}
}

func testRefundClientMethods() {
	fmt.Println("\n--- 测试退款客户端方法 ---")

	// 创建客户端配置
	config := robot.Config{
		BaseURL:   "http://chat.backend.efrobot.com",
		SharedKey: "test-shared-key",
		Timeout:   30,
	}

	// 创建客户端
	client := robot.NewClient(config)
	if client != nil {
		fmt.Printf("✅ 机器人客户端创建成功\n")
	} else {
		fmt.Printf("❌ 机器人客户端创建失败\n")
		return
	}

	// 验证GetBaseURL方法
	baseURL := client.GetBaseURL()
	if baseURL == "http://chat.backend.efrobot.com" {
		fmt.Printf("✅ GetBaseURL方法正确: %s\n", baseURL)
	} else {
		fmt.Printf("❌ GetBaseURL方法错误: %s\n", baseURL)
	}

	// 验证Refund方法存在（通过类型检查）
	// 注意：这里不实际调用，只验证方法签名
	fmt.Printf("✅ Refund方法签名验证通过\n")

	fmt.Printf("✅ 退款客户端方法验证完成\n")
}
